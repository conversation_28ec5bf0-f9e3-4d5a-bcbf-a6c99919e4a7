# Database
POSTGRES_DB=supermcp
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=**************************************/supermcp

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_REDIRECT_URL=http://localhost:3000/auth/callback

# JWT
JWT_SECRET=your_super_secret_jwt_key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Redis
REDIS_URL=redis://redis:6379/0

# External APIs
TAVILY_API_KEY=your_tavily_api_key
CONTEXT7_MCP_URL=wss://context7-mcp-server-url

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=SuperMCP

# Development
DEBUG=true
ENVIRONMENT=development

# Celery
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
SECRET_KEY=your_super_secret_key_for_fastapi

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=SuperMCP
PROJECT_VERSION=1.0.0