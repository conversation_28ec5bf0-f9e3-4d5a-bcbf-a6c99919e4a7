#!/usr/bin/env python3
"""
Debug script to find the exact location of the slice error
"""

import sys
import os
import asyncio
import traceback
import logging

# Add the backend directory to the Python path
sys.path.insert(0, '/Users/<USER>/repos/supermcp/backend')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_slice_error():
    """Test the intelligent analysis service to find the slice error"""
    
    try:
        # Import the service
        from app.services.intelligent_analysis_service import IntelligentAnalysisService
        
        logger.info("=== INITIALIZING INTELLIGENT ANALYSIS SERVICE ===")
        service = IntelligentAnalysisService()
        
        logger.info("=== CALLING ANALYZE_REPOSITORY_COMPREHENSIVELY ===")
        
        # Get a real GitHub token from environment or config
        import os
        github_token = os.getenv('GITHUB_TOKEN')
        if not github_token:
            # Try to get from the app config
            try:
                from app.config import settings
                github_token = settings.github_token or "dummy_token"
            except:
                github_token = "dummy_token"

        logger.info(f"Using GitHub token: {'***' + github_token[-4:] if len(github_token) > 4 else 'dummy'}")

        # Call the problematic function with the exact same data that's failing
        result = await service.analyze_repository_comprehensively(
            repo_owner="strapi",
            repo_name="strapi",
            github_token=github_token
        )
        
        logger.info("=== ANALYSIS COMPLETED SUCCESSFULLY ===")
        logger.info(f"Result keys: {list(result.keys()) if result else 'None'}")
        
    except Exception as e:
        logger.error("=== ERROR CAUGHT ===")
        logger.error(f"Error: {str(e)}")
        logger.error(f"Error type: {type(e)}")
        
        # Check if it's the slice error
        if "unhashable type: 'slice'" in str(e):
            logger.error("=== FOUND THE SLICE ERROR! ===")
            logger.error("Full traceback:")
            logger.error(traceback.format_exc())
            
            # Try to find the exact line
            tb = traceback.extract_tb(e.__traceback__)
            for frame in tb:
                if 'slice' in str(frame) or 'intelligent_analysis' in frame.filename:
                    logger.error(f"Problematic frame: {frame.filename}:{frame.lineno} in {frame.name}")
                    logger.error(f"Code: {frame.line}")
        else:
            logger.error("Different error:")
            logger.error(traceback.format_exc())

def main():
    """Main function to run the test"""
    logger.info("Starting slice error debug test...")
    
    try:
        asyncio.run(test_slice_error())
    except Exception as e:
        logger.error(f"Failed to run test: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
