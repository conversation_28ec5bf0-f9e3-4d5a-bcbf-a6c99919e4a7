#!/bin/bash

# SuperMCP Development Environment Reset Script
# This script completely resets the development environment to a fresh state

set -e  # Exit on any error

echo "🧹 SuperMCP Development Environment Reset"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

# Step 1: Stop all services
print_status "Stopping all Docker services..."
docker-compose -f docker-compose.dev.yml down --remove-orphans

# Step 2: Remove all containers, volumes, and networks
print_status "Removing all containers, volumes, and networks..."
docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans

# Step 3: Remove any dangling images (optional)
print_status "Cleaning up Docker images..."
docker image prune -f

# Step 4: Start services
print_status "Starting fresh Docker services..."
docker-compose -f docker-compose.dev.yml up -d

# Step 5: Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 10

# Check if database is ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U postgres -d supermcp > /dev/null 2>&1; then
        print_success "Database is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Database failed to start after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Waiting for database... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Step 6: Run database migrations
print_status "Running database migrations..."
docker-compose -f docker-compose.dev.yml exec -T backend alembic upgrade head

# Step 7: Populate MCP discovery data
print_status "Populating MCP discovery data..."
if [ -f "backend/migrations/add_mcp_discovery.sql" ]; then
    docker cp backend/migrations/add_mcp_discovery.sql supermcp-db:/tmp/add_mcp_discovery.sql
    docker-compose -f docker-compose.dev.yml exec -T db psql -U postgres -d supermcp -f /tmp/add_mcp_discovery.sql
    print_success "MCP discovery data populated"
else
    print_warning "MCP discovery migration file not found, skipping..."
fi

# Step 8: Create a test user
print_status "Creating test user..."
docker-compose -f docker-compose.dev.yml exec -T db psql -U postgres -d supermcp -c "
INSERT INTO users (github_id, username, email, avatar_url, full_name, github_token, is_active, created_at, updated_at) 
VALUES ('12345', 'testuser', '<EMAIL>', 'https://avatars.githubusercontent.com/u/12345', 'Test User', 'test_token_123', true, NOW(), NOW()) 
ON CONFLICT (github_id) DO NOTHING;
" > /dev/null

# Step 9: Generate auth token
print_status "Generating authentication token..."
AUTH_TOKEN=$(docker-compose -f docker-compose.dev.yml exec -T backend python -c "
from app.utils.auth import create_access_token
from app.database import SessionLocal
from app.models import User

db = SessionLocal()
user = db.query(User).filter(User.username == 'testuser').first()
if user:
    token = create_access_token(data={'sub': str(user.id)})
    print(token)
else:
    print('ERROR: Test user not found')
db.close()
" 2>/dev/null)

if [[ $AUTH_TOKEN == *"ERROR"* ]] || [ -z "$AUTH_TOKEN" ]; then
    print_error "Failed to generate authentication token"
    exit 1
fi

# Step 10: Update test auth page
print_status "Updating test authentication page..."
cat > frontend/public/test-auth.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>SuperMCP Test Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .token { font-family: monospace; font-size: 12px; word-break: break-all; }
    </style>
</head>
<body>
    <h1>🔐 SuperMCP Test Authentication</h1>
    <p>This page helps you set authentication tokens for development testing.</p>
    
    <div>
        <button onclick="setToken()">🔑 Set Fresh Token</button>
        <button onclick="clearToken()">🗑️ Clear Token</button>
        <button onclick="checkToken()">🔍 Check Token</button>
    </div>
    
    <div id="status"></div>

    <script>
        function setToken() {
            const token = '${AUTH_TOKEN}';
            localStorage.setItem('auth_token', token);
            document.getElementById('status').innerHTML = '<p class="success">✅ Fresh authentication token set successfully!</p><p class="info">User: testuser (ID: from fresh database)</p>';
        }

        function clearToken() {
            localStorage.removeItem('auth_token');
            document.getElementById('status').innerHTML = '<p class="error">🗑️ Authentication token cleared!</p>';
        }

        function checkToken() {
            const token = localStorage.getItem('auth_token');
            if (token) {
                document.getElementById('status').innerHTML = '<p class="info">🔍 Token exists:</p><div class="token">' + token + '</div>';
            } else {
                document.getElementById('status').innerHTML = '<p class="error">❌ No authentication token found</p>';
            }
        }
    </script>
</body>
</html>
EOF

# Step 11: Final status check
print_status "Checking service status..."
sleep 5

# Check if all services are running
SERVICES=("db" "redis" "backend" "celery-worker")
ALL_HEALTHY=true

for service in "${SERVICES[@]}"; do
    if docker-compose -f docker-compose.dev.yml ps $service | grep -q "Up"; then
        print_success "$service is running"
    else
        print_error "$service is not running"
        ALL_HEALTHY=false
    fi
done

# Final summary
echo ""
echo "🎉 Development Environment Reset Complete!"
echo "=========================================="

if [ "$ALL_HEALTHY" = true ]; then
    print_success "All services are running successfully"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Visit: http://localhost:3000/test-auth.html"
    echo "2. Click 'Set Fresh Token' to authenticate"
    echo "3. Navigate to: http://localhost:3000/dashboard"
    echo "4. Start testing with a completely fresh environment!"
    echo ""
    echo "🔗 Available URLs:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo "   Test Auth: http://localhost:3000/test-auth.html"
else
    print_error "Some services failed to start. Check the logs:"
    echo "   docker-compose -f docker-compose.dev.yml logs"
fi
