#!/bin/bash

# SuperMCP Development Environment Reset Script
# This script completely resets the development environment to a fresh state

set -e  # Exit on any error

echo "🧹 SuperMCP Development Environment Reset"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

# Step 1: Stop all services
print_status "Stopping all Docker services..."
docker-compose -f docker-compose.dev.yml down --remove-orphans

# Step 2: Remove all containers, volumes, and networks
print_status "Removing all containers, volumes, and networks..."
docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans

# Step 3: Remove any dangling images (optional)
print_status "Cleaning up Docker images..."
docker image prune -f

# Step 4: Start services
print_status "Starting fresh Docker services..."
docker-compose -f docker-compose.dev.yml up -d

# Step 5: Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 10

# Check if database is ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U postgres -d supermcp > /dev/null 2>&1; then
        print_success "Database is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Database failed to start after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Waiting for database... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Step 6: Run database migrations
print_status "Running database migrations..."
docker-compose -f docker-compose.dev.yml exec -T backend alembic upgrade head

# Step 7: Populate MCP discovery data (required for recommendations)
print_status "Populating MCP server database..."
if [ -f "backend/migrations/add_mcp_discovery.sql" ]; then
    docker cp backend/migrations/add_mcp_discovery.sql supermcp-db:/tmp/add_mcp_discovery.sql
    docker-compose -f docker-compose.dev.yml exec -T db psql -U postgres -d supermcp -f /tmp/add_mcp_discovery.sql
    print_success "MCP server database populated (12 categories, 14 servers)"
else
    print_warning "MCP discovery migration file not found, skipping..."
fi

# Step 8: Wait for backend to be ready
print_status "Waiting for backend to be ready..."
sleep 5

# Step 9: Final status check
print_status "Checking service status..."
sleep 5

# Check if all services are running
SERVICES=("db" "redis" "backend" "celery-worker" "frontend")
ALL_HEALTHY=true

for service in "${SERVICES[@]}"; do
    if docker-compose -f docker-compose.dev.yml ps $service | grep -q "Up"; then
        print_success "$service is running"
    else
        print_error "$service is not running"
        ALL_HEALTHY=false
    fi
done

# Final summary
echo ""
echo "🎉 Development Environment Reset Complete!"
echo "=========================================="

if [ "$ALL_HEALTHY" = true ]; then
    print_success "All services are running successfully"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Visit: http://localhost:3000"
    echo "2. Sign up/login with your GitHub account"
    echo "3. Start analyzing repositories!"
    echo ""
    echo "🔗 Available URLs:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
else
    print_error "Some services failed to start. Check the logs:"
    echo "   docker-compose -f docker-compose.dev.yml logs"
fi
