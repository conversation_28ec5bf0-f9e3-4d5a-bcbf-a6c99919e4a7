# =============================================================================
# SuperMCP Production Configuration
# =============================================================================

# Environment
ENVIRONMENT=production
DEBUG=false
NODE_ENV=production

# =============================================================================
# Database Configuration
# =============================================================================
POSTGRES_DB=supermcp
POSTGRES_USER=supermcp
POSTGRES_PASSWORD=your_secure_database_password_here
POSTGRES_PORT=5432

# Database URL for SQLAlchemy
DATABASE_URL=*************************************************************************/supermcp

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_PORT=6379
REDIS_URL=redis://supermcp-redis:6379/0
REDIS_AUTH_CMD=--requirepass your_redis_password_here

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://supermcp-redis:6379/0
CELERY_RESULT_BACKEND=redis://supermcp-redis:6379/0
CELERY_LOG_LEVEL=info
CELERY_CONCURRENCY=4

# =============================================================================
# Application Ports
# =============================================================================
BACKEND_PORT=8000
FRONTEND_PORT=3000
HTTP_PORT=80
HTTPS_PORT=443
FLOWER_PORT=5555

# =============================================================================
# Docker Configuration
# =============================================================================
RESTART_POLICY=unless-stopped
BACKEND_DOCKERFILE=Dockerfile.prod
FRONTEND_DOCKERFILE=Dockerfile.prod

# =============================================================================
# GitHub OAuth (REPLACE WITH YOUR PRODUCTION VALUES)
# =============================================================================
GITHUB_CLIENT_ID=your_production_github_client_id
GITHUB_CLIENT_SECRET=your_production_github_client_secret
GITHUB_REDIRECT_URL=https://yourdomain.com/auth/callback

# =============================================================================
# JWT & Security (GENERATE SECURE KEYS FOR PRODUCTION)
# =============================================================================
JWT_SECRET=your_secure_jwt_secret_here_64_chars_minimum
SECRET_KEY=your_secure_secret_key_here

# =============================================================================
# External APIs
# =============================================================================
TAVILY_API_KEY=your_tavily_api_key_here
CONTEXT7_MCP_URL=https://mcp.context7.com/sse

# =============================================================================
# AI APIs for repository analysis
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# CORS Configuration
# =============================================================================
CORS_ORIGINS=["https://yourdomain.com","https://www.yourdomain.com"]

# =============================================================================
# Frontend Configuration
# =============================================================================
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_NAME=SuperMCP

# =============================================================================
# Monitoring (Flower)
# =============================================================================
FLOWER_USER=admin
FLOWER_PASSWORD=your_secure_flower_password_here
