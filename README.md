# SuperMCP

**Analyze GitHub repositories to determine MCP server potential and recommend existing MCP servers for dependencies.**

## Overview

SuperMCP is a comprehensive tool that analyzes GitHub repositories to:
- Assess the feasibility of creating MCP (Model Context Protocol) servers
- Identify existing MCP servers that could be used with your dependencies
- Provide detailed implementation recommendations and guides

## Features

- 🔍 **Multi-language Analysis**: Support for Python, JavaScript/TypeScript, Go, Java, and more
- 🚀 **Real-time Processing**: Background analysis with live status updates
- 🔐 **GitHub Integration**: Secure OAuth authentication with access to private repositories
- 🧠 **AI-Powered Recommendations**: Intelligent matching of dependencies to existing MCP servers
- 📊 **Comprehensive Reports**: Detailed analysis results with actionable insights
- 🎨 **Modern UI**: Clean, responsive interface built with Next.js and shadcn/ui

## Tech Stack

### Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Task Queue**: Celery with Redis
- **Authentication**: GitHub OAuth + JWT

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **UI Library**: shadcn/ui components
- **Styling**: Tailwind CSS with custom indigo theme

### Infrastructure
- **Development**: Docker Compose
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/surenganne/supermcp.git
   cd supermcp
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Configuration

### GitHub OAuth Setup

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App with:
   - Application name: SuperMCP
   - Homepage URL: http://localhost:3000
   - Authorization callback URL: http://localhost:3000/auth/callback
3. Copy the Client ID and Client Secret to your `.env` file

### External Services

- **Tavily API**: Sign up at tavily.com for web search capabilities
- **Context7 MCP**: Configure your Context7 MCP server URL

## API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Interactive API explorer with request/response examples

## Project Structure

```
supermcp/
├── backend/           # FastAPI backend application
├── frontend/          # Next.js frontend application
├── docker-compose.dev.yml    # Development environment
├── docker-compose.prod.yml   # Production environment
└── nginx.conf        # Production reverse proxy configuration
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests, please open an issue on GitHub.

---

**Built with ❤️ using FastAPI, Next.js, and the power of MCP servers**