# SuperMCP

**Open source tool to analyze GitHub repositories and generate MCP (Model Context Protocol) servers with AI-powered analysis.**

## Overview

SuperMCP is a comprehensive, open source tool that analyzes GitHub repositories to:
- Assess the feasibility of creating MCP (Model Context Protocol) servers
- Generate production-ready MCP server implementations
- Provide detailed implementation recommendations and guides
- Support multiple programming languages (Python, TypeScript, Go, Rust, Java, C#)

## Features

- 🔍 **Multi-language Analysis**: Support for Python, JavaScript/TypeScript, Go, Java, Rust, C#, and more
- 🚀 **Real-time Processing**: Background analysis with live status updates
- 🔐 **GitHub Integration**: Secure OAuth authentication with repository access
- 🧠 **AI-Powered Code Generation**: Generate complete MCP servers with implementation guides
- 📊 **Comprehensive Reports**: Detailed analysis results with feasibility scores
- 🎨 **Clean, Modern UI**: Professional interface designed for developers
- 🔓 **Open Source**: MIT license - deploy and customize your own instance

## Tech Stack

### Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Task Queue**: Celery with Redis
- **Authentication**: GitHub OAuth + JWT

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **UI Library**: shadcn/ui components
- **Styling**: Tailwind CSS with clean, modern design

### Infrastructure
- **Development**: Docker Compose
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7

## Quick Start

### 🚀 Fresh Development Environment

**Complete Reset (recommended for first setup):**
```bash
./reset-dev-environment.sh
```

**Database Only Reset (faster, keeps containers running):**
```bash
./cleanup-database.sh
```

The complete reset will:
- � Stop all Docker services
- �️ Remove containers, volumes, and networks
- � Start fresh services
- �️ Run database migrations

The database reset will:
- �️ Clean all database tables
- �️ Run database migrations
- 🧹 Clear Redis cache

After running either script, visit `http://localhost:3000` and sign up with your GitHub account.

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/surenganne/supermcp.git
   cd supermcp
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Configuration

### GitHub OAuth Setup

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App with:
   - Application name: SuperMCP
   - Homepage URL: http://localhost:3000
   - Authorization callback URL: http://localhost:3000/auth/callback
3. Copy the Client ID and Client Secret to your `.env` file

### External Services (Required for MCP Discovery)

- **Tavily API**: Sign up at tavily.com for real-time MCP server discovery
- **Context7 MCP**: Configure your Context7 MCP server URL for documentation search

**Important**: SuperMCP uses real-time search to discover MCP servers dynamically. Set up these API keys to enable MCP recommendations.

## API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Interactive API explorer with request/response examples

## Project Structure

```
supermcp/
├── backend/           # FastAPI backend application
├── frontend/          # Next.js frontend application
├── docker-compose.dev.yml    # Development environment
├── docker-compose.prod.yml   # Production environment
└── nginx.conf        # Production reverse proxy configuration
```

## UI/UX Design

SuperMCP features a clean, modern interface designed for professional developers:

- **Streamlined Authentication**: Direct login flow without unnecessary intermediate pages
- **Clean Dashboard**: Focus on essential information and actions
- **Professional Design**: Minimal use of gradients and animations for a business-ready appearance
- **Consistent Layout**: Unified design language across all pages
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Development Scripts

### 🧹 `reset-dev-environment.sh`
Complete development environment reset script:
- Stops and removes all Docker containers/volumes
- Starts fresh services with clean database
- Runs database migrations
- **Usage**: `./reset-dev-environment.sh`

### 🗄️ `cleanup-database.sh`
Quick database cleanup without Docker restart:
- Cleans all database tables (preserves schema)
- Runs database migrations
- Clears Redis cache
- **Usage**: `./cleanup-database.sh`

## Open Source & Customization

SuperMCP is open source (MIT license) and designed for easy customization:

- **Environment Configuration**: Update API keys and settings via environment variables
- **Custom Deployment**: Deploy to your own infrastructure
- **UI Customization**: Modify the clean, professional design to match your brand
- **Feature Extensions**: Add new analysis capabilities or integrations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests, please open an issue on GitHub.

---

**Built with ❤️ using FastAPI, Next.js, and the power of MCP servers**