# Production Environment Variables
# Copy this file to .env.prod and update with your production values

# Database Configuration
POSTGRES_PASSWORD=your_secure_postgres_password_here
DATABASE_URL=postgresql://supermcp:${POSTGRES_PASSWORD}@postgres:5432/supermcp

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://redis:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Security Keys (Generate with: openssl rand -hex 32)
JWT_SECRET=your_jwt_secret_key_here_32_chars_minimum
SECRET_KEY=your_secret_key_here_32_chars_minimum

# GitHub OAuth (Production App)
GITHUB_CLIENT_ID=your_production_github_client_id
GITHUB_CLIENT_SECRET=your_production_github_client_secret
GITHUB_REDIRECT_URL=https://supermcp.com/auth/callback

# CORS Origins
CORS_ORIGINS=["https://supermcp.com","https://www.supermcp.com"]

# External API Keys
TAVILY_API_KEY=your_tavily_api_key_here
CONTEXT7_MCP_URL=your_context7_mcp_url_here

# Flower Monitoring
FLOWER_USER=admin
FLOWER_PASSWORD=your_secure_flower_password_here

# Application Configuration
ENVIRONMENT=production
DEBUG=false
NODE_ENV=production

# Frontend Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.supermcp.com/api/v1
NEXT_PUBLIC_APP_NAME=SuperMCP

# SSL Certificate paths (if using custom certificates)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=INFO

# Backup Configuration
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=your_s3_backup_bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1