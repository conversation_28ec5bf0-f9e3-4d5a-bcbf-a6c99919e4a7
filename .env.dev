# Database
POSTGRES_DB=supermcp
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# GitHub OAuth (REPLACE WITH YOUR VALUES)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_REDIRECT_URL=http://localhost:3000/auth/callback

# JWT & Security (FOR DEVELOPMENT ONLY - GENERATE SECURE KEYS FOR PRODUCTION)
JWT_SECRET=dev_jwt_secret_key_not_for_production_use_only
SECRET_KEY=dev_fastapi_secret_key_not_for_production_use_only

# External APIs (Optional)
TAVILY_API_KEY=your_tavily_api_key
CONTEXT7_MCP_URL=wss://context7-mcp-server-url

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=SuperMCP