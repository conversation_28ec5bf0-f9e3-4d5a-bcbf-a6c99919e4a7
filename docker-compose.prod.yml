version: '3.8'

services:
  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: supermcp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - supermcp-network

  # Next.js Frontend (Production Build)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: supermcp-frontend-prod
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.supermcp.com/api/v1
    restart: unless-stopped
    networks:
      - supermcp-network

  # FastAPI Backend (Production)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: supermcp-backend-prod
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=postgresql://supermcp:${POSTGRES_PASSWORD}@postgres:5432/supermcp
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GITHUB_REDIRECT_URL=https://supermcp.com/auth/callback
      - CORS_ORIGINS=["https://supermcp.com","https://www.supermcp.com"]
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - CONTEXT7_MCP_URL=${CONTEXT7_MCP_URL}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - supermcp-network

  # PostgreSQL Database (Production)
  postgres:
    image: postgres:15-alpine
    container_name: supermcp-postgres-prod
    environment:
      - POSTGRES_DB=supermcp
      - POSTGRES_USER=supermcp
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U supermcp -d supermcp"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - supermcp-network

  # Redis (Production with Authentication)
  redis:
    image: redis:7-alpine
    container_name: supermcp-redis-prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - supermcp-network

  # Celery Worker (Production)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: supermcp-celery-worker-prod
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=postgresql://supermcp:${POSTGRES_PASSWORD}@postgres:5432/supermcp
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - CONTEXT7_MCP_URL=${CONTEXT7_MCP_URL}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - supermcp-network

  # Celery Beat Scheduler (Production)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: supermcp-celery-beat-prod
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=postgresql://supermcp:${POSTGRES_PASSWORD}@postgres:5432/supermcp
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - supermcp-network

  # Flower (Celery Monitoring)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: supermcp-flower-prod
    command: celery -A app.tasks.celery_app flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_BASIC_AUTH=${FLOWER_USER}:${FLOWER_PASSWORD}
    ports:
      - "5555:5555"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - supermcp-network

volumes:
  postgres_data:
  redis_data:

networks:
  supermcp-network:
    driver: bridge