#!/bin/bash

# SuperMCP Database Cleanup Script
# This script cleans only the database data while keeping containers running

set -e

echo "🗄️ SuperMCP Database Cleanup"
echo "============================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Create cleanup SQL script
cat > /tmp/cleanup_db.sql << 'EOF'
-- SuperMCP Database Cleanup Script
-- This script will clean up all data while preserving the schema

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- Clean up analysis-related tables
TRUNCATE TABLE dependencies CASCADE;
TRUNCATE TABLE repo_analyses CASCADE;

-- Clean up MCP discovery tables (if they exist - dynamic data from real-time search)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mcp_servers') THEN
        TRUNCATE TABLE mcp_servers CASCADE;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mcp_categories') THEN
        TRUNCATE TABLE mcp_categories CASCADE;
    END IF;
END $$;

-- Clean up detected integrations (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'detected_integrations') THEN
        TRUNCATE TABLE detected_integrations CASCADE;
    END IF;
END $$;

-- Clean up users table (optional - uncomment if you want to remove users too)
-- TRUNCATE TABLE users CASCADE;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Reset sequences to start from 1
ALTER SEQUENCE repo_analyses_id_seq RESTART WITH 1;
ALTER SEQUENCE dependencies_id_seq RESTART WITH 1;

-- Reset MCP sequences if they exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'mcp_servers_id_seq') THEN
        ALTER SEQUENCE mcp_servers_id_seq RESTART WITH 1;
    END IF;
    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'mcp_categories_id_seq') THEN
        ALTER SEQUENCE mcp_categories_id_seq RESTART WITH 1;
    END IF;
END $$;

-- Reset detected_integrations sequence if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'detected_integrations_id_seq') THEN
        ALTER SEQUENCE detected_integrations_id_seq RESTART WITH 1;
    END IF;
END $$;

-- Reset users sequence if you truncated users table
-- ALTER SEQUENCE users_id_seq RESTART WITH 1;

-- Verify cleanup
SELECT 'repo_analyses' as table_name, COUNT(*) as remaining_records FROM repo_analyses
UNION ALL
SELECT 'dependencies' as table_name, COUNT(*) as remaining_records FROM dependencies
UNION ALL
SELECT 'users' as table_name, COUNT(*) as remaining_records FROM users;

-- Show success message
SELECT 'Database cleanup completed successfully!' as status;
EOF

# Copy and run cleanup script
print_status "Cleaning database..."
docker cp /tmp/cleanup_db.sql supermcp-db:/tmp/cleanup_db.sql
docker exec supermcp-db psql -U postgres -d supermcp -f /tmp/cleanup_db.sql

# Run database migrations to ensure schema is up to date
print_status "Running database migrations..."
docker-compose exec -T backend alembic upgrade head

# MCP discovery uses real-time search - no static data needed
print_status "MCP discovery configured for real-time search..."
print_success "Ready to discover MCP servers dynamically using Tavily API and Context7 MCP"

# Clear Redis cache
print_status "Clearing Redis cache..."
docker exec supermcp-redis redis-cli FLUSHALL > /dev/null

# Cleanup temp files
rm -f /tmp/cleanup_db.sql

print_success "Database cleanup completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Visit: http://localhost:3000"
echo "2. Sign up/login with your GitHub account"
echo "3. Start analyzing repositories with clean data!"
