#!/bin/bash

# SuperMCP Database Cleanup Script
# This script cleans only the database data while keeping containers running

set -e

echo "🗄️ SuperMCP Database Cleanup"
echo "============================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Create cleanup SQL script
cat > /tmp/cleanup_db.sql << 'EOF'
-- SuperMCP Database Cleanup Script
-- This script will clean up all data while preserving the schema

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- Clean up analysis-related tables
TRUNCATE TABLE dependencies CASCADE;
TRUNCATE TABLE repo_analyses CASCADE;

-- Clean up MCP discovery tables (if they exist)
TRUNCATE TABLE mcp_servers CASCADE;
TRUNCATE TABLE mcp_categories CASCADE;

-- Clean up detected integrations (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'detected_integrations') THEN
        TRUNCATE TABLE detected_integrations CASCADE;
    END IF;
END $$;

-- Clean up users table (optional - uncomment if you want to remove users too)
-- TRUNCATE TABLE users CASCADE;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Reset sequences to start from 1
ALTER SEQUENCE repo_analyses_id_seq RESTART WITH 1;
ALTER SEQUENCE dependencies_id_seq RESTART WITH 1;
ALTER SEQUENCE mcp_servers_id_seq RESTART WITH 1;
ALTER SEQUENCE mcp_categories_id_seq RESTART WITH 1;

-- Reset detected_integrations sequence if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'detected_integrations_id_seq') THEN
        ALTER SEQUENCE detected_integrations_id_seq RESTART WITH 1;
    END IF;
END $$;

-- Reset users sequence if you truncated users table
-- ALTER SEQUENCE users_id_seq RESTART WITH 1;

-- Verify cleanup
SELECT 'repo_analyses' as table_name, COUNT(*) as remaining_records FROM repo_analyses
UNION ALL
SELECT 'dependencies' as table_name, COUNT(*) as remaining_records FROM dependencies
UNION ALL
SELECT 'mcp_servers' as table_name, COUNT(*) as remaining_records FROM mcp_servers
UNION ALL
SELECT 'mcp_categories' as table_name, COUNT(*) as remaining_records FROM mcp_categories
UNION ALL
SELECT 'users' as table_name, COUNT(*) as remaining_records FROM users;

-- Show success message
SELECT 'Database cleanup completed successfully!' as status;
EOF

# Copy and run cleanup script
print_status "Cleaning database..."
docker cp /tmp/cleanup_db.sql supermcp-db:/tmp/cleanup_db.sql
docker exec supermcp-db psql -U postgres -d supermcp -f /tmp/cleanup_db.sql

# Re-populate MCP discovery data
print_status "Re-populating MCP discovery data..."
if [ -f "backend/migrations/add_mcp_discovery.sql" ]; then
    docker cp backend/migrations/add_mcp_discovery.sql supermcp-db:/tmp/add_mcp_discovery.sql
    docker exec supermcp-db psql -U postgres -d supermcp -f /tmp/add_mcp_discovery.sql
    print_success "MCP discovery data restored"
else
    echo "Warning: MCP discovery migration file not found"
fi

# Create test user
print_status "Creating test user..."
docker exec supermcp-db psql -U postgres -d supermcp -c "
INSERT INTO users (github_id, username, email, avatar_url, full_name, github_token, is_active, created_at, updated_at) 
VALUES ('12345', 'testuser', '<EMAIL>', 'https://avatars.githubusercontent.com/u/12345', 'Test User', 'test_token_123', true, NOW(), NOW()) 
ON CONFLICT (github_id) DO NOTHING;
" > /dev/null

# Generate new auth token
print_status "Generating new authentication token..."
AUTH_TOKEN=$(docker-compose -f docker-compose.dev.yml exec -T backend python -c "
from app.utils.auth import create_access_token
from app.database import SessionLocal
from app.models import User

db = SessionLocal()
user = db.query(User).filter(User.username == 'testuser').first()
if user:
    token = create_access_token(data={'sub': str(user.id)})
    print(token)
db.close()
" 2>/dev/null)

# Update test auth page
print_status "Updating test authentication page..."
sed -i.bak "s/const token = '[^']*'/const token = '$AUTH_TOKEN'/" frontend/public/test-auth.html

# Clear Redis cache
print_status "Clearing Redis cache..."
docker exec supermcp-redis redis-cli FLUSHALL > /dev/null

# Cleanup temp files
rm -f /tmp/cleanup_db.sql

print_success "Database cleanup completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Visit: http://localhost:3000/test-auth.html"
echo "2. Click 'Set Fresh Token' to authenticate"
echo "3. Start testing with clean data!"
