services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: supermcp-db
    environment:
      POSTGRES_DB: supermcp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - supermcp-network

  # Redis for Celery and Vector storage
  redis:
    image: redis:7-alpine
    container_name: supermcp-redis
    ports:
      - "6379:6379"
    command: redis-server --protected-mode no
    volumes:
      - redis_data:/data
    networks:
      - supermcp-network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: supermcp-backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    environment:
      - DATABASE_URL=**************************************/supermcp
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_VECTOR_URL=redis://redis:6379/1
      - DEBUG=true
      - ENVIRONMENT=development
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GITHUB_REDIRECT_URL=http://localhost:3000/auth/callback
      - JWT_SECRET=${JWT_SECRET:-4f8b2c9e1a7d3f6b8e2c5a9f1d4e7b3c9f2e5a8d1c4f7b9e2a5d8c1f4e7b3c6a9}
      - SECRET_KEY=${SECRET_KEY:-SuperMCP#2025}
      - CORS_ORIGINS=["http://localhost:3000","http://localhost:3001"]
    depends_on:
      - db
      - redis
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - supermcp-network

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: supermcp-celery-worker
    volumes:
      - ./backend:/app
    environment:
      - DATABASE_URL=**************************************/supermcp
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_VECTOR_URL=redis://redis:6379/1
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GITHUB_REDIRECT_URL=http://localhost:3000/auth/callback
      - JWT_SECRET=${JWT_SECRET:-4f8b2c9e1a7d3f6b8e2c5a9f1d4e7b9e2a5d8c1f4e7b3c6a9}
      - SECRET_KEY=${SECRET_KEY:-SuperMCP#2025}
    depends_on:
      - db
      - redis
    command: celery -A app.tasks.celery_app worker --loglevel=info
    networks:
      - supermcp-network

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: supermcp-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_APP_NAME=SuperMCP
    depends_on:
      - backend
    command: npm run dev
    networks:
      - supermcp-network

volumes:
  postgres_data:
  redis_data:

networks:
  supermcp-network:
    driver: bridge