#!/bin/bash

echo "🔄 Stopping all SuperMCP containers..."
docker-compose -f docker-compose.dev.yml down

echo "🧹 Cleaning up Docker containers and images..."
docker container prune -f
docker image prune -f

echo "🗄️ Starting PostgreSQL database..."
docker-compose -f docker-compose.dev.yml up -d db

echo "🚀 Starting Redis..."
docker-compose -f docker-compose.dev.yml up -d redis

echo "⏳ Waiting for database and Redis to be ready..."
sleep 10

echo "🏗️ Building and starting backend..."
docker-compose -f docker-compose.dev.yml up -d --build backend

echo "⏳ Waiting for backend to be ready..."
sleep 15

echo "👷 Starting Celery worker..."
docker-compose -f docker-compose.dev.yml up -d celery-worker

echo "🌐 Building and starting frontend..."
docker-compose -f docker-compose.dev.yml up -d --build frontend

echo "✅ All services should be running now!"
echo ""
echo "🔍 Check service status:"
echo "Backend: http://localhost:8000/health"
echo "Frontend: http://localhost:3000"
echo ""
echo "📋 To view logs:"
echo "Database logs: docker logs supermcp-db -f"
echo "Backend logs: docker logs supermcp-backend -f"
echo "Celery logs: docker logs supermcp-celery-worker -f"
echo "Frontend logs: docker logs supermcp-frontend -f"
echo ""
echo "🧪 To test OAuth:"
echo "1. Go to http://localhost:3000"
echo "2. Click 'Sign in with GitHub'"
echo "3. Complete OAuth flow"