'use client'

import { useEffect, useState, Suspense, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth'
import { Loader2, CheckCircle, XCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

function CallbackContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [error, setError] = useState<string>('')
  const { handleCallback } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()

  const processedRef = useRef(false)

  useEffect(() => {
    if (processedRef.current) {
      return
    }

    const code = searchParams.get('code')
    const error = searchParams.get('error')

    if (error) {
      setStatus('error')
      setError(`GitHub OAuth error: ${error}`)
      processedRef.current = true
      return
    }

    if (!code) {
      setStatus('error')
      setError('No authorization code received from GitHub')
      processedRef.current = true
      return
    }

    handleOAuthCallback(code)
    processedRef.current = true
  }, [searchParams])

  const handleOAuthCallback = async (code: string) => {
    try {
      const success = await handleCallback(code)
      if (success) {
        setStatus('success')
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard')
        }, 2000)
      } else {
        setStatus('error')
        setError('Authentication failed')
      }
    } catch (err) {
      console.error('OAuth callback error:', err)
      setStatus('error')
      setError('Authentication failed. Please try again.')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {status === 'loading' && (
                <Loader2 className="w-12 h-12 animate-spin text-primary" />
              )}
              {status === 'success' && (
                <CheckCircle className="w-12 h-12 text-green-500" />
              )}
              {status === 'error' && (
                <XCircle className="w-12 h-12 text-red-500" />
              )}
            </div>
            
            <CardTitle>
              {status === 'loading' && 'Signing you in...'}
              {status === 'success' && 'Success!'}
              {status === 'error' && 'Authentication Failed'}
            </CardTitle>
            
            <CardDescription>
              {status === 'loading' && 'Please wait while we complete your authentication.'}
              {status === 'success' && 'You have been successfully signed in. Redirecting to dashboard...'}
              {status === 'error' && error}
            </CardDescription>
          </CardHeader>

          {status === 'error' && (
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Button asChild className="flex-1">
                  <Link href="/auth/login">Try Again</Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/">Go Home</Link>
                </Button>
              </div>
            </CardContent>
          )}

          {status === 'success' && (
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/dashboard">Go to Dashboard</Link>
              </Button>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  )
}

export default function CallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4">
                <Loader2 className="w-12 h-12 animate-spin text-primary" />
              </div>
              <CardTitle>Loading...</CardTitle>
              <CardDescription>Please wait while we process your authentication.</CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  )
}