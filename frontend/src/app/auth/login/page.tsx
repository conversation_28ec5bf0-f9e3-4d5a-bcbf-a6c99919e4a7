'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LoginButton } from '@/components/auth/login-button'
import { useAuth } from '@/hooks/use-auth'
import { Loader2 } from 'lucide-react'

export default function LoginPage() {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Super<span className="text-primary">MCP</span>
          </h1>
          <p className="text-gray-600">
            Sign in to analyze your repositories
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Sign in</CardTitle>
            <CardDescription className="text-center">
              Connect with GitHub to get started with repository analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            <LoginButton size="lg" className="w-full" />
            
            <div className="text-center text-sm text-gray-600">
              <p>
                By signing in, you agree to our terms of service and privacy policy.
              </p>
              <p className="mt-2">
                We only access public repository information and your basic profile.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-gray-500">
          <p>
            Don&apos;t have a GitHub account?{' '}
            <a 
              href="https://github.com/join" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              Create one here
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}