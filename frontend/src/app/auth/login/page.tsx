'use client'

import { LoginButton } from '@/components/auth/login-button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import { Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function LoginPage() {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <img
              src="/supermcp.png"
              alt="SuperMCP Logo"
              className="h-12 w-12 object-contain"
            />
            <h1 className="text-3xl font-bold text-gray-900">
              SuperMCP
            </h1>
          </div>
          <p className="text-gray-600">
            Sign in to analyze your repositories
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">Sign in with GitHub</CardTitle>
            <CardDescription className="text-center">
              Connect your GitHub account to get started
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <LoginButton size="lg" className="w-full" />

            <div className="text-center text-sm text-gray-500">
              <p>
                We only access public repository information and your basic profile.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-gray-500">
          <p>
            Don&apos;t have a GitHub account?{' '}
            <a
              href="https://github.com/join"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Create one here
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}