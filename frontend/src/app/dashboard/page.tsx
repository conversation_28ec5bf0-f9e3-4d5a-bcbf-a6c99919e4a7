'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ChartBarIcon, 
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  RocketLaunchIcon,
  ArrowRightIcon,
  PlusIcon,
  CpuChipIcon,
  StarIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import apiClient, { Analysis, AnalysisStats } from '@/lib/api';

interface QuickAction {
  name: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
  iconBg: string;
}

const quickActions: QuickAction[] = [
  {
    name: 'New Analysis',
    description: 'Analyze any GitHub repository with AI-powered multi-dimensional assessment',
    href: '/dashboard/analysis/new',
    icon: SparklesIcon,
    gradient: 'from-indigo-600 to-purple-600',
    iconBg: 'bg-gradient-to-r from-indigo-500 to-purple-500'
  },
  {
    name: 'View All Analyses',
    description: 'Browse your complete analysis history and track MCP server generation',
    href: '/dashboard/analysis',
    icon: ChartBarIcon,
    gradient: 'from-blue-600 to-cyan-600',
    iconBg: 'bg-gradient-to-r from-blue-500 to-cyan-500'
  },
];

export default function DashboardPage() {
  const [stats, setStats] = useState<AnalysisStats | null>(null);
  const [recentAnalyses, setRecentAnalyses] = useState<Analysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [statsData, historyData] = await Promise.all([
          apiClient.getAnalysisStats(),
          apiClient.getAnalysisHistory(0, 5)
        ]);
        
        setStats(statsData);
        setRecentAnalyses(historyData);
      } catch (err) {
        console.error('Failed to fetch dashboard data:', err);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'analyzing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-700 bg-green-50 ring-green-600/20';
      case 'failed':
        return 'text-red-700 bg-red-50 ring-red-600/20';
      case 'analyzing':
        return 'text-blue-700 bg-blue-50 ring-blue-600/20';
      default:
        return 'text-yellow-700 bg-yellow-50 ring-yellow-600/20';
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Hero Welcome Section */}
        <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 shadow-2xl shadow-indigo-500/25">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-white mb-3">Welcome to SuperMCP</h1>
                <p className="text-xl text-indigo-100 leading-relaxed">
                  Transform any GitHub repository into powerful MCP servers with AI-powered analysis
                </p>
              </div>
              <div className="hidden lg:block">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <RocketLaunchIcon className="h-12 w-12 text-white mb-2" />
                  <p className="text-sm text-indigo-100 font-semibold">Ready to Launch</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-xl shadow-blue-100/50 hover:shadow-2xl hover:shadow-blue-100/50 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-600 mb-2">Total Analyses</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{stats.total_analyses}</p>
                    <p className="text-sm text-gray-500 mt-1">All repository analyses</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <ChartBarIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-white via-white to-green-50/30 shadow-xl shadow-green-100/50 hover:shadow-2xl hover:shadow-green-100/50 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-600 mb-2">Completed</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">{stats.completed_analyses}</p>
                    <p className="text-sm text-gray-500 mt-1">Successfully analyzed</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <CheckCircleIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-white via-white to-purple-50/30 shadow-xl shadow-purple-100/50 hover:shadow-2xl hover:shadow-purple-100/50 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-600 mb-2">In Progress</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">{stats.pending_analyses}</p>
                    <p className="text-sm text-gray-500 mt-1">Currently processing</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <ClockIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Quick Actions */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Quick Actions</h2>
              <p className="text-gray-600 mt-1">Get started with your repository analysis journey</p>
            </div>
            <TrophyIcon className="h-8 w-8 text-yellow-500" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {quickActions.map((action, index) => (
              <Card key={action.name} className="border-0 shadow-xl shadow-gray-100/50 hover:shadow-2xl hover:shadow-indigo-100/50 transition-all duration-300 group overflow-hidden hover:scale-105">
                <Link href={action.href}>
                  <div className={`h-2 bg-gradient-to-r ${action.gradient}`}></div>
                  <CardContent className="p-8">
                    <div className="flex items-start justify-between mb-4">
                      <div className={`w-14 h-14 ${action.iconBg} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <action.icon className="h-7 w-7 text-white" />
                      </div>
                      <Badge className="bg-indigo-100 text-indigo-700 hover:bg-indigo-200">
                        #{index + 1}
                      </Badge>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors duration-300">
                      {action.name}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-4">
                      {action.description}
                    </p>
                    <div className="flex items-center text-indigo-600 font-semibold group-hover:text-indigo-700 transition-colors duration-300">
                      Get Started
                      <ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>
        </div>

        {/* Enhanced Recent Analyses */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Recent Analyses</h2>
              <p className="text-gray-600 mt-1">Track your latest repository assessments</p>
            </div>
            <Link href="/dashboard/analysis">
              <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg shadow-indigo-500/25">
                <ChartBarIcon className="mr-2 h-4 w-4" />
                View All
              </Button>
            </Link>
          </div>
          
          {recentAnalyses.length > 0 ? (
            <div className="space-y-4">
              {recentAnalyses.map((analysis) => (
                <Card key={analysis.id} className="border-0 shadow-lg shadow-gray-100/50 hover:shadow-xl hover:shadow-indigo-100/50 transition-all duration-300 group overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 shadow-lg">
                          {getStatusIcon(analysis.status)}
                        </div>
                        <div>
                          <Link 
                            href={`/dashboard/analysis/${analysis.id}`}
                            className="text-lg font-semibold text-gray-900 hover:text-indigo-600 transition-colors duration-200 flex items-center group/link"
                          >
                            {analysis.repo_owner}/{analysis.repo_name}
                            <ArrowRightIcon className="ml-2 h-4 w-4 opacity-0 group-hover/link:opacity-100 transition-opacity duration-200" />
                          </Link>
                          <p className="text-sm text-gray-500 mt-1">
                            {new Date(analysis.created_at).toLocaleDateString('en-US', {
                              month: 'long',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        {analysis.mcp_feasibility_score && (
                          <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${
                            analysis.mcp_feasibility_score >= 75 
                              ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/25' 
                              : analysis.mcp_feasibility_score >= 50 
                              ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-lg shadow-yellow-400/25' 
                              : 'bg-gradient-to-r from-red-400 to-pink-400 text-white shadow-lg shadow-red-400/25'
                          }`}>
                            <StarIcon className="h-4 w-4 mr-2" />
                            {Math.round(analysis.mcp_feasibility_score)}
                          </div>
                        )}
                        <Badge 
                          className={`px-3 py-1 text-xs font-semibold ${
                            analysis.status === 'completed' ? 'bg-green-100 text-green-800' :
                            analysis.status === 'analyzing' ? 'bg-blue-100 text-blue-800' :
                            analysis.status === 'failed' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {analysis.status}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="border-0 bg-gradient-to-br from-white via-white to-indigo-50/30 shadow-xl shadow-indigo-100/50">
              <CardContent className="text-center py-16">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <SparklesIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Ready to Start Analyzing?</h3>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  Transform your GitHub repositories into powerful MCP servers with AI-powered analysis.
                </p>
                <Link href="/dashboard/analysis/new">
                  <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg shadow-indigo-500/25 px-8 py-3">
                    <PlusIcon className="mr-2 h-5 w-5" />
                    Analyze Your First Repository
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>

        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex">
                <XCircleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}