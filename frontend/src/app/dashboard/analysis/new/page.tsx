'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ChartBarIcon, 
  MagnifyingGlassIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import apiClient from '@/lib/api';

export default function NewAnalysisPage() {
  const [repoUrl, setRepoUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const parseGitHubUrl = (url: string) => {
    try {
      // Handle various GitHub URL formats
      const patterns = [
        /github\.com\/([^\/]+)\/([^\/]+?)(?:\.git)?(?:\/.*)?$/,
        /^([^\/]+)\/([^\/]+)$/
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
          return {
            owner: match[1],
            repo: match[2].replace('.git', '')
          };
        }
      }

      throw new Error('Invalid URL format');
    } catch (err) {
      throw new Error('Unable to parse repository URL');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      if (!repoUrl.trim()) {
        throw new Error('Please enter a repository URL');
      }

      const { owner, repo } = parseGitHubUrl(repoUrl.trim());
      
      // Create the analysis
      const analysis = await apiClient.createAnalysis({
        repo_url: repoUrl.trim(),
        repo_name: repo,
        repo_owner: owner
      });

      // Redirect to the analysis detail page
      router.push(`/dashboard/analysis/${analysis.id}`);
    } catch (err: any) {
      console.error('Failed to create analysis:', err);
      console.error('Error details:', {
        status: err.response?.status,
        data: err.response?.data,
        message: err.message,
        isAuthenticated: apiClient.isAuthenticated()
      });
      
      if (err.response?.status === 401) {
        setError('Please log in to create an analysis');
        router.push('/auth/login');
      } else if (err.code === 'ERR_NETWORK') {
        setError('Network error - please check if you are logged in');
      } else {
        setError(err.response?.data?.detail || err.message || 'Failed to start analysis');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRepoUrl(e.target.value);
    setError(null); // Clear error when user types
  };

  const exampleUrls = [
    'https://github.com/microsoft/vscode',
    'https://github.com/facebook/react',
    'https://github.com/vercel/next.js',
    'openai/openai-python'
  ];

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Modern Hero Header */}
        <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 shadow-2xl shadow-indigo-500/25">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div>
                <Link href="/dashboard/analysis" className="inline-flex items-center text-indigo-100 hover:text-white transition-colors duration-200 mb-4">
                  <ArrowLeftIcon className="mr-2 h-5 w-5" />
                  Back to Analysis Dashboard
                </Link>
                <h1 className="text-4xl font-bold text-white mb-3">New Repository Analysis</h1>
                <p className="text-xl text-indigo-100 leading-relaxed">
                  Transform any GitHub repository into powerful MCP servers with AI-powered multi-dimensional analysis
                </p>
              </div>
              <div className="hidden lg:block">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <ChartBarIcon className="h-12 w-12 text-white mb-2" />
                  <p className="text-sm text-indigo-100 font-semibold">AI-Powered Analysis</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">

          {/* Main Form */}
          <Card className="border-0 bg-gradient-to-br from-white via-white to-gray-50/30 shadow-xl shadow-gray-100/50 hover:shadow-2xl hover:shadow-indigo-100/50 transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-t-lg relative">
              <div className="absolute inset-0 bg-black/10"></div>
              <CardTitle className="text-2xl flex items-center relative z-10">
                <ChartBarIcon className="mr-3 h-7 w-7" />
                Repository Analysis
              </CardTitle>
              <CardDescription className="text-indigo-100 text-lg relative z-10">
                Enter the GitHub repository URL to begin AI-powered analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="space-y-4">
                  <Label htmlFor="repo-url" className="text-lg font-semibold text-gray-700">GitHub Repository URL</Label>
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
                    <Input
                      id="repo-url"
                      type="url"
                      placeholder="https://github.com/owner/repository or owner/repository"
                      value={repoUrl}
                      onChange={handleUrlChange}
                      className="pl-12 h-14 text-lg border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 rounded-xl shadow-sm"
                      disabled={loading}
                    />
                  </div>
                  <p className="text-gray-500 leading-relaxed">
                    Enter a full GitHub URL or just the owner/repository format. Our AI will analyze the repository structure, dependencies, and business logic to generate comprehensive MCP server recommendations.
                  </p>
                </div>

                {error && (
                  <Alert variant="destructive" className="border-red-200 bg-red-50">
                    <ExclamationTriangleIcon className="h-5 w-5" />
                    <AlertDescription className="text-red-700">{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
                  <Link href="/dashboard/analysis">
                    <Button type="button" variant="outline" disabled={loading} className="w-full sm:w-auto px-8 py-3 border-gray-200 hover:bg-gray-50">
                      Cancel
                    </Button>
                  </Link>
                  <Button 
                    type="submit" 
                    disabled={loading || !repoUrl.trim()}
                    className="w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg shadow-indigo-500/25 px-8 py-3"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3" />
                        Starting Analysis...
                      </>
                    ) : (
                      <>
                        <ChartBarIcon className="mr-3 h-5 w-5" />
                        Start AI Analysis
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Information & Process Section */}
          <div className="space-y-6">
            <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-xl shadow-blue-100/50">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-t-lg relative">
                <div className="absolute inset-0 bg-black/10"></div>
                <CardTitle className="text-2xl flex items-center relative z-10">
                  <SparklesIcon className="mr-3 h-7 w-7" />
                  How AI Analysis Works
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8 space-y-6">
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg">
                      1
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Repository Deep Scan</h4>
                      <p className="text-gray-600 leading-relaxed">
                        AI analyzes repository structure, dependencies, API endpoints, and business logic patterns to understand the core functionality.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg">
                      2
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">MCP Feasibility Scoring</h4>
                      <p className="text-gray-600 leading-relaxed">
                        Advanced algorithms calculate a 0-100 feasibility score based on API quality, automation potential, and integration opportunities.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg">
                      3
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Smart Tool Generation</h4>
                      <p className="text-gray-600 leading-relaxed">
                        AI generates 3-50+ tailored MCP tools across 7 categories with implementation roadmaps and business value analysis.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-white via-white to-green-50/30 shadow-xl shadow-green-100/50">
              <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-t-lg relative">
                <div className="absolute inset-0 bg-black/10"></div>
                <CardTitle className="text-xl flex items-center relative z-10">
                  <LightBulbIcon className="mr-3 h-6 w-6" />
                  Try These Examples
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid gap-3">
                  {exampleUrls.map((url, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => setRepoUrl(url)}
                      className="flex items-center p-4 text-left border border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all duration-200 group"
                      disabled={loading}
                    >
                      <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                        <span className="text-white font-semibold text-sm">{index + 1}</span>
                      </div>
                      <span className="font-medium text-gray-700 group-hover:text-green-700 transition-colors duration-200">
                        {url}
                      </span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Feature Highlights Section */}
        <div className="max-w-6xl mx-auto">
          <Card className="border-0 bg-gradient-to-br from-white via-white to-purple-50/30 shadow-xl shadow-purple-100/50">
            <CardHeader className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-t-lg relative">
              <div className="absolute inset-0 bg-black/10"></div>
              <CardTitle className="text-2xl flex items-center relative z-10">
                <RocketLaunchIcon className="mr-3 h-7 w-7" />
                Enhanced Analysis Features
              </CardTitle>
              <CardDescription className="text-purple-100 text-lg relative z-10">
                Powered by advanced AI for comprehensive repository understanding
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                    <CpuChipIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">7-Category Analysis</h4>
                    <p className="text-sm text-gray-600">Core Operations, Data Management, Integration, Utilities, Monitoring, Automation, and Reporting</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <SparklesIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Business Logic Understanding</h4>
                    <p className="text-sm text-gray-600">AI identifies core business processes and automation opportunities</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <ChartBarIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Smart Scoring</h4>
                    <p className="text-sm text-gray-600">Multi-dimensional feasibility assessment with confidence metrics</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}