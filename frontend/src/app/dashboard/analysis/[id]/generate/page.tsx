'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeftIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  CodeBracketIcon,
  CogIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  FolderIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RepositoryTree } from '@/components/ui/repository-tree';
import apiClient from '@/lib/api';

interface MCPCapabilities {
  tools: Array<{
    name: string;
    description: string;
    inputSchema: any;
  }>;
  resources: Array<{
    uri: string;
    name: string;
    description: string;
  }>;
  prompts: Array<{
    name: string;
    description: string;
  }>;
  server_info: {
    name: string;
    description: string;
    mcp_score: number;
  };
}

interface MCPPreview {
  server_name: string;
  language: string;
  capabilities: MCPCapabilities;
  features: {
    tools_count: number;
    resources_count: number;
    prompts_count: number;
    has_api_integration: boolean;
    has_custom_tools: boolean;
  };
}

export default function MCPGenerationPage() {
  const router = useRouter();
  const params = useParams();
  const analysisId = params.id as string;
  
  const [preview, setPreview] = useState<MCPPreview | null>(null);
  const [analysis, setAnalysis] = useState<any>(null);
  const [language, setLanguage] = useState('python');
  const [serverName, setServerName] = useState('');
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [generatedFilePath, setGeneratedFilePath] = useState<string | null>(null);
  const [generatedProjectStructure, setGeneratedProjectStructure] = useState<string[] | null>(null);
  const [setupInstructions, setSetupInstructions] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalysisAndPreview();
  }, [analysisId]);

  useEffect(() => {
    if (analysis) {
      loadPreview();
    }
  }, [language, serverName]);

  const loadAnalysisAndPreview = async () => {
    try {
      setLoading(true);
      
      // Load analysis data first
      const analysisData = await apiClient.getAnalysis(parseInt(analysisId));
      setAnalysis(analysisData);
      
      // Auto-detect primary language from repository
      if (analysisData.analysis_results?.repository_info?.language) {
        const repoLanguage = analysisData.analysis_results.repository_info.language.toLowerCase();
        if (['python', 'javascript', 'typescript'].includes(repoLanguage)) {
          setLanguage(repoLanguage);
        } else if (repoLanguage === 'javascript') {
          setLanguage('javascript');
        } else {
          // Default to the most common web languages
          const languages = analysisData.analysis_results?.languages || {};
          if (languages['TypeScript']) setLanguage('typescript');
          else if (languages['JavaScript']) setLanguage('javascript');
          else if (languages['Python']) setLanguage('python');
        }
      }
      
      // Set default server name
      if (analysisData.repo_name && !serverName) {
        setServerName(`${analysisData.repo_name}-mcp-server`);
      }
      
      // Now load preview
      await loadPreview(analysisData);
      
    } catch (err) {
      console.error('Failed to load analysis:', err);
      setError('Failed to load analysis data');
      setLoading(false);
    }
  };

  const loadPreview = async (analysisData?: any) => {
    try {
      if (!analysisData && !analysis) return;
      
      const currentAnalysis = analysisData || analysis;
      setLoading(true);
      
      const previewData = await apiClient.getMCPPreview(
        parseInt(analysisId), 
        language, 
        serverName || undefined
      );
      setPreview(previewData);
      
      if (!serverName && previewData.server_name) {
        setServerName(previewData.server_name);
      }
    } catch (err) {
      console.error('Failed to load MCP preview:', err);
      setError('Failed to load MCP server preview. Please check if the analysis is completed.');
    } finally {
      setLoading(false);
    }
  };

  const generateServer = async () => {
    if (!preview) return;

    try {
      setGenerating(true);
      setError(null);
      setGeneratedFilePath(null);
      setGeneratedProjectStructure(null);
      setSetupInstructions('');

      const generationResponse = await apiClient.generateMCPServer({
        analysis_id: parseInt(analysisId),
        selected_tools: preview.capabilities.tools.map(tool => tool.name),
        target_language: language,
        customization_options: {
          server_name: serverName,
        },
      });

      const zipFilePath = generationResponse.zip_file_path;
      const projectStructure = generationResponse.project_structure;
      const setupInstructions = generationResponse.setup_instructions;

      if (!zipFilePath) {
        throw new Error("Generation did not return a file path.");
      }

      setGeneratedFilePath(zipFilePath);
      setGeneratedProjectStructure(projectStructure || []);
      setSetupInstructions(setupInstructions || '');

    } catch (err) {
      console.error('MCP generation failed:', err);
      setError('Failed to generate MCP server. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  const downloadServer = async () => {
    if (!generatedFilePath) return;

    try {
      setDownloading(true);
      setError(null);

      const blob = await apiClient.downloadMCPServer(parseInt(analysisId), generatedFilePath);

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${serverName || 'mcp-server'}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (err) {
      console.error('MCP download failed:', err);
      setError('Failed to download MCP server. Please try again.');
    } finally {
      setDownloading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href={`/dashboard/analysis/${analysisId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Back to Analysis
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Generate MCP Server</h1>
            <p className="text-sm text-gray-600">
              Create a complete MCP server implementation from your repository analysis
            </p>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Configuration Panel */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CogIcon className="mr-2 h-5 w-5" />
                  Configuration
                </CardTitle>
                <CardDescription>
                  Configure your MCP server generation options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Programming Language</Label>
                  <Select value={language} onValueChange={setLanguage}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="python">Python</SelectItem>
                      <SelectItem value="javascript">JavaScript</SelectItem>
                      <SelectItem value="typescript">TypeScript</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    Choose the programming language for your MCP server
                    {analysis?.analysis_results?.repository_info?.language && 
                      ` (Repository is primarily ${analysis.analysis_results.repository_info.language})`
                    }
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="server-name">Server Name</Label>
                  <Input
                    id="server-name"
                    value={serverName}
                    onChange={(e) => setServerName(e.target.value)}
                    placeholder="my-mcp-server"
                  />
                  <p className="text-xs text-gray-500">
                    Custom name for your MCP server
                  </p>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={generateServer}
                    disabled={generating || !preview || !serverName.trim()}
                    className="w-full"
                    size="lg"
                  >
                    {generating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <RocketLaunchIcon className="mr-2 h-5 w-5" />
                        Generate Server
                      </>
                    )}
                  </Button>
                  
                  {generatedFilePath && (
                    <>
                      <Alert className="border-green-200 bg-green-50">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                          <strong>Generation Complete!</strong> Your MCP server has been generated successfully. 
                          Review the file structure below and download when ready.
                        </AlertDescription>
                      </Alert>
                      
                      <Button
                        onClick={downloadServer}
                        disabled={downloading}
                        className="w-full"
                        size="lg"
                        variant="outline"
                      >
                        {downloading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600 mr-2" />
                            Downloading...
                          </>
                        ) : (
                          <>
                            <DocumentArrowDownIcon className="mr-2 h-5 w-5" />
                            Download Server
                          </>
                        )}
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Repository Info */}
            {analysis && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Repository Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Repository:</span>
                    <span className="text-sm font-medium">{analysis.repo_owner}/{analysis.repo_name}</span>
                  </div>
                  {analysis.analysis_results?.repository_info?.language && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Primary Language:</span>
                      <Badge variant="outline">{analysis.analysis_results.repository_info.language}</Badge>
                    </div>
                  )}
                  {analysis.analysis_results?.repository_info?.stars !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Stars:</span>
                      <span className="text-sm font-medium">{analysis.analysis_results.repository_info.stars.toLocaleString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Files:</span>
                    <span className="text-sm font-medium">{analysis.analysis_results?.code_structure?.file_count || 'N/A'}</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Languages Breakdown */}
            {analysis?.analysis_results?.languages && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Code Languages</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {Object.entries(analysis.analysis_results.languages)
                    .sort(([,a], [,b]) => (b as number) - (a as number))
                    .slice(0, 4)
                    .map(([lang, bytes]) => (
                      <div key={lang} className="flex justify-between text-sm">
                        <span>{lang}</span>
                        <span className="text-gray-600">{((bytes as number) / 1024).toFixed(1)} KB</span>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            {preview && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Generation Preview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">MCP Score:</span>
                    <Badge variant={preview.capabilities.server_info.mcp_score >= 70 ? 'default' : 'secondary'}>
                      {Math.round(preview.capabilities.server_info.mcp_score)}/100
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tools:</span>
                    <span className="text-sm font-medium">{preview.features.tools_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Resources:</span>
                    <span className="text-sm font-medium">{preview.features.resources_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Prompts:</span>
                    <span className="text-sm font-medium">{preview.features.prompts_count}</span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Preview Panel */}
          <div className="lg:col-span-2 space-y-6">
            {preview && (
              <>
                {/* What Will Be Built */}
                <Card className="border-green-200 bg-green-50">
                  <CardHeader>
                    <CardTitle className="flex items-center text-green-800">
                      <RocketLaunchIcon className="mr-2 h-5 w-5" />
                      Your MCP Server: {preview.server_name}
                    </CardTitle>
                    <CardDescription className="text-green-700">
                      Based on analysis of <strong>{analysis?.repo_owner}/{analysis?.repo_name}</strong>, we&apos;ll generate a complete MCP server 
                      that can be used with Claude Desktop and other MCP-compatible clients.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {preview.features.tools_count}
                        </div>
                        <div className="text-sm text-gray-600">Tools</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {preview.features.resources_count}
                        </div>
                        <div className="text-sm text-gray-600">Resources</div>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">
                          {preview.features.prompts_count}
                        </div>
                        <div className="text-sm text-gray-600">Prompts</div>
                      </div>
                      <div className="text-center p-3 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">
                          {Math.round(preview.capabilities.server_info.mcp_score)}
                        </div>
                        <div className="text-sm text-gray-600">MCP Score</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Generated Files Structure */}
                {generatedProjectStructure && generatedProjectStructure.length > 0 && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardHeader>
                      <CardTitle className="flex items-center text-blue-800">
                        <FolderIcon className="mr-2 h-5 w-5" />
                        Generated Files & Structure
                      </CardTitle>
                      <CardDescription className="text-blue-700">
                        Your MCP server has been generated successfully! Review the files below and download when ready.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {generatedProjectStructure.map((file, index) => {
                          const isFolder = file.endsWith('/') || (!file.includes('.') && file.includes('/'));
                          const fileName = file.split('/').pop() || file;
                          const depth = (file.match(/\//g) || []).length;
                          
                          return (
                            <div 
                              key={index} 
                              className="flex items-center space-x-2 text-sm py-1"
                              style={{ paddingLeft: `${depth * 20}px` }}
                            >
                              {isFolder ? (
                                <FolderIcon className="h-4 w-4 text-blue-600" />
                              ) : (
                                <DocumentIcon className="h-4 w-4 text-gray-600" />
                              )}
                              <span className={isFolder ? "font-medium text-blue-800" : "text-gray-700"}>
                                {fileName}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                      
                      {setupInstructions && (
                        <div className="mt-4 p-3 bg-white rounded-lg border">
                          <h4 className="font-medium text-gray-900 mb-2">Setup Instructions</h4>
                          <pre className="text-xs text-gray-600 whitespace-pre-wrap max-h-32 overflow-y-auto">
                            {setupInstructions}
                          </pre>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Tools */}
                {preview.capabilities.tools.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <CodeBracketIcon className="mr-2 h-5 w-5" />
                        MCP Tools ({preview.capabilities.tools.length})
                      </CardTitle>
                      <CardDescription>
                        Tools that will be available through the MCP protocol
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {preview.capabilities.tools.slice(0, 5).map((tool, index) => (
                          <div key={index} className="p-3 border rounded-lg">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-gray-900">{tool.name}</h4>
                                <p className="text-sm text-gray-600">{tool.description}</p>
                              </div>
                              <Badge variant="outline">Tool</Badge>
                            </div>
                          </div>
                        ))}
                        {preview.capabilities.tools.length > 5 && (
                          <p className="text-sm text-gray-500 text-center">
                            +{preview.capabilities.tools.length - 5} more tools...
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Resources */}
                {preview.capabilities.resources.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <EyeIcon className="mr-2 h-5 w-5" />
                        MCP Resources ({preview.capabilities.resources.length})
                      </CardTitle>
                      <CardDescription>
                        Resources that can be accessed through the MCP protocol
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {preview.capabilities.resources.slice(0, 3).map((resource, index) => (
                          <div key={index} className="p-3 border rounded-lg">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-gray-900">{resource.name}</h4>
                                <p className="text-sm text-gray-600">{resource.description}</p>
                                <p className="text-xs text-gray-500 font-mono">{resource.uri}</p>
                              </div>
                              <Badge variant="outline">Resource</Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Features */}
                <Card>
                  <CardHeader>
                    <CardTitle>Generated Features</CardTitle>
                    <CardDescription>
                      What will be included in your MCP server package
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Complete MCP protocol implementation</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">API endpoint wrappers</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Resource access layer</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Configuration files</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Comprehensive documentation</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Deployment scripts</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Docker configuration</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Error handling & logging</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Repository Structure */}
                {analysis?.analysis_results?.repository_tree && (
                  <RepositoryTree 
                    tree={analysis.analysis_results.repository_tree}
                    indicators={analysis.analysis_results.repository_tree.mcp_indicators}
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}