'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ChartBarIcon, 
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  FunnelIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RepositoryTree } from '@/components/ui/repository-tree';
import apiClient, { Analysis, Dependency } from '@/lib/api';

export default function AnalysisDetailPage() {
  const params = useParams();
  const router = useRouter();
  const analysisId = parseInt(params.id as string);
  
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [dependencies, setDependencies] = useState<Dependency[]>([]);
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dependencyFilter, setDependencyFilter] = useState<string>('all');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchAnalysisData();
  }, [analysisId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (analysis && (analysis.status === 'analyzing' || analysis.status === 'pending') && autoRefresh) {
      interval = setInterval(() => {
        fetchAnalysisStatus();
      }, 3000); // Refresh every 3 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [analysis?.status, autoRefresh]);

  const fetchAnalysisData = async () => {
    try {
      const [analysisData, dependenciesData] = await Promise.all([
        apiClient.getAnalysis(analysisId),
        apiClient.getAnalysisDependencies(analysisId)
      ]);
      
      setAnalysis(analysisData);
      setDependencies(dependenciesData);
      
      // Fetch status if analysis is in progress
      if (analysisData.status === 'analyzing' || analysisData.status === 'pending') {
        fetchAnalysisStatus();
      }
    } catch (err) {
      console.error('Failed to fetch analysis data:', err);
      setError('Failed to load analysis data');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalysisStatus = async () => {
    try {
      const statusData = await apiClient.getAnalysisStatus(analysisId);
      setStatus(statusData);
      
      // If status changed to completed/failed, refresh full data
      if (statusData.status !== analysis?.status && 
          (statusData.status === 'completed' || statusData.status === 'failed')) {
        setAutoRefresh(false);
        fetchAnalysisData();
      }
    } catch (err) {
      console.error('Failed to fetch analysis status:', err);
    }
  };

  const handleRetry = async () => {
    try {
      await apiClient.retryAnalysis(analysisId);
      setAutoRefresh(true);
      setTimeout(() => fetchAnalysisData(), 1000);
    } catch (err) {
      console.error('Failed to retry analysis:', err);
    }
  };

  const handleExport = async (format: 'json' | 'csv') => {
    try {
      const data = await apiClient.exportAnalysis(analysisId, format);
      
      // Create and download file
      const blob = new Blob([format === 'json' ? JSON.stringify(data, null, 2) : data.csv_data], {
        type: format === 'json' ? 'application/json' : 'text/csv'
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis-${analysisId}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export analysis:', err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'analyzing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'success',
      failed: 'destructive',
      analyzing: 'default',
      pending: 'secondary'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  const getMCPScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 75) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressPercentage = () => {
    if (!status || !status.task_info) return 0;
    const { current = 0, total = 100 } = status.task_info;
    return Math.round((current / total) * 100);
  };

  const filteredDependencies = dependencies.filter(dep => {
    if (dependencyFilter === 'all') return true;
    if (dependencyFilter === 'high-mcp') return (dep.mcp_potential || 0) >= 0.7;
    if (dependencyFilter === 'medium-mcp') return (dep.mcp_potential || 0) >= 0.4 && (dep.mcp_potential || 0) < 0.7;
    if (dependencyFilter === 'low-mcp') return (dep.mcp_potential || 0) < 0.4;
    return dep.language === dependencyFilter;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !analysis) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <XCircleIcon className="mx-auto h-12 w-12 text-red-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
          <p className="mt-1 text-sm text-gray-500">{error || 'Analysis not found'}</p>
          <div className="mt-6">
            <Link href="/dashboard/analysis">
              <Button>Back to Analyses</Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/analysis">
              <Button variant="outline" size="sm">
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                {getStatusIcon(analysis.status)}
                <span className="ml-2">{analysis.repo_owner}/{analysis.repo_name}</span>
              </h1>
              <p className="text-sm text-gray-600">
                Analysis started {new Date(analysis.created_at).toLocaleString()}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {getStatusBadge(analysis.status)}
            
            {analysis.status === 'failed' && (
              <Button size="sm" onClick={handleRetry} className="border-orange-200 text-orange-600 hover:bg-orange-50">
                <ArrowPathIcon className="mr-2 h-4 w-4" />
                Retry
              </Button>
            )}
            
            {analysis.status === 'completed' && (
              <Link href={`/dashboard/analysis/${analysisId}/mcp-suggestions`}>
                <Button size="sm" className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 shadow-lg shadow-indigo-500/25">
                  <ChartBarIcon className="mr-2 h-4 w-4" />
                  AI MCP Suggestions
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Progress Section */}
        {(analysis.status === 'analyzing' || analysis.status === 'pending') && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ClockIcon className="mr-2 h-5 w-5 animate-spin" />
                Analysis in Progress
              </CardTitle>
              <CardDescription>
                {status?.task_info?.status || 'Starting analysis...'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Progress value={getProgressPercentage()} className="w-full" />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Progress: {getProgressPercentage()}%</span>
                  {status?.task_info && (
                    <span>{status.task_info.current || 0} / {status.task_info.total || 100}</span>
                  )}
                </div>
                
                {/* Debug information */}
                {status && (
                  <details className="text-xs text-gray-500">
                    <summary className="cursor-pointer">Debug Info</summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(status, null, 2)}
                    </pre>
                  </details>
                )}
                
                {autoRefresh && (
                  <p className="text-xs text-gray-500">
                    Auto-refreshing every 3 seconds...
                  </p>
                )}
                
                {/* Show additional info if task seems stuck */}
                {analysis.status === 'analyzing' && 
                 (!status?.task_info || getProgressPercentage() === 0) && (
                  <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
                    <p className="font-medium">Analysis may be taking longer than expected.</p>
                    <p className="text-xs mt-1">
                      This could be due to repository size, network issues, or backend processing. 
                      The analysis will continue in the background.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Message */}
        {analysis.error_message && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex">
                <XCircleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Analysis Failed</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{analysis.error_message}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* MCP Score */}
        {analysis.mcp_feasibility_score !== undefined && (
          <Card className="border-0 bg-gradient-to-br from-white via-white to-indigo-50/30 shadow-lg shadow-indigo-100/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold text-gray-900">MCP Feasibility Score</CardTitle>
              <CardDescription className="text-gray-600">
                Overall assessment of MCP server implementation potential
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-8">
                <div className="relative">
                  <div className={`text-8xl font-black ${
                    analysis.mcp_feasibility_score >= 75 
                      ? 'bg-gradient-to-br from-green-500 to-emerald-600 bg-clip-text text-transparent' 
                      : analysis.mcp_feasibility_score >= 50 
                      ? 'bg-gradient-to-br from-yellow-500 to-orange-500 bg-clip-text text-transparent' 
                      : 'bg-gradient-to-br from-red-500 to-pink-500 bg-clip-text text-transparent'
                  }`}>
                    {Math.round(analysis.mcp_feasibility_score)}
                  </div>
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                    <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
                      analysis.mcp_feasibility_score >= 75 
                        ? 'bg-green-100 text-green-800' 
                        : analysis.mcp_feasibility_score >= 50 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      /100
                    </div>
                  </div>
                </div>
                <div className="flex-1 space-y-4">
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-3">Score breakdown:</div>
                    <div className="relative">
                      <Progress 
                        value={analysis.mcp_feasibility_score} 
                        className="w-full h-4 bg-gray-200"
                      />
                      <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 h-full rounded-full opacity-20"></div>
                    </div>
                  </div>
                  <div className={`p-4 rounded-xl border-l-4 ${
                    analysis.mcp_feasibility_score >= 75 
                      ? 'bg-green-50 border-green-400 text-green-800' 
                      : analysis.mcp_feasibility_score >= 50 
                      ? 'bg-yellow-50 border-yellow-400 text-yellow-800' 
                      : 'bg-red-50 border-red-400 text-red-800'
                  }`}>
                    <div className="font-semibold text-sm">
                      {analysis.mcp_feasibility_score >= 75 && "🎉 Excellent MCP server candidate"}
                      {analysis.mcp_feasibility_score >= 50 && analysis.mcp_feasibility_score < 75 && "✨ Good MCP server potential"}
                      {analysis.mcp_feasibility_score < 50 && "💡 Consider enhancements for better MCP potential"}
                    </div>
                    <div className="text-xs mt-1 opacity-80">
                      {analysis.mcp_feasibility_score >= 75 && "This repository has outstanding characteristics for MCP server implementation"}
                      {analysis.mcp_feasibility_score >= 50 && analysis.mcp_feasibility_score < 75 && "This repository shows solid potential with some optimization opportunities"}
                      {analysis.mcp_feasibility_score < 50 && "This repository may need additional structure or APIs to maximize MCP potential"}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Repository Structure */}
        {analysis.status === 'completed' && (
          <Card className="border-0 bg-gradient-to-br from-white via-white to-gray-50/30 shadow-lg shadow-gray-100/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
                <ChartBarIcon className="mr-3 h-6 w-6 text-indigo-600" />
                Repository Structure
              </CardTitle>
              <CardDescription className="text-gray-600">
                Detailed analysis of repository architecture and components
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analysis.analysis_results?.repository_tree ? (
                <RepositoryTree 
                  tree={analysis.analysis_results.repository_tree}
                  indicators={analysis.analysis_results.repository_tree.mcp_indicators}
                />
              ) : analysis.analysis_results?.code_structure ? (
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center">
                      <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                      Languages & Files
                    </h4>
                    <div className="space-y-3">
                      {Object.entries(analysis.analysis_results.code_structure.languages || {}).map(([lang, bytes]) => (
                        <div key={lang} className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                          <span className="font-medium text-gray-700">{lang}</span>
                          <div className="text-right">
                            <span className="text-sm font-semibold text-gray-900">{((bytes as number) / 1024).toFixed(1)} KB</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Structure Features
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                        <span className="text-gray-700">Command Line Interface</span>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          analysis.analysis_results.code_structure.has_cli 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {analysis.analysis_results.code_structure.has_cli ? '✓ Available' : '✗ Not Available'}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                        <span className="text-gray-700">API Endpoints</span>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          analysis.analysis_results.code_structure.has_api 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {analysis.analysis_results.code_structure.has_api ? '✓ Available' : '✗ Not Available'}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-white border border-gray-100 hover:shadow-sm transition-shadow">
                        <span className="text-gray-700">Database Integration</span>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          analysis.analysis_results.code_structure.has_database 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {analysis.analysis_results.code_structure.has_database ? '✓ Available' : '✗ Not Available'}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-indigo-50 border border-indigo-100">
                        <span className="text-indigo-700 font-medium">Total Files</span>
                        <div className="px-3 py-1 rounded-full text-sm font-bold bg-indigo-100 text-indigo-800">
                          {analysis.analysis_results.code_structure.file_count || 0}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <EyeIcon className="mx-auto h-16 w-16 text-gray-300" />
                  <h3 className="mt-4 text-lg font-medium text-gray-900">Repository structure not available</h3>
                  <p className="mt-2 text-gray-500 max-w-md mx-auto">
                    The repository structure could not be loaded or analyzed. This may be due to access restrictions or analysis limitations.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}