'use client';

import DashboardLayout from '@/components/layout/dashboard-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import apiClient from '@/lib/api';
import {
    ArrowDownTrayIcon,
    ArrowLeftIcon,
    ChartBarIcon,
    CheckCircleIcon,
    ClockIcon,
    Cog6ToothIcon,
    CommandLineIcon,
    CpuChipIcon,
    DocumentIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    FolderIcon,
    LightBulbIcon,
    RocketLaunchIcon,
    ServerIcon,
    SparklesIcon
} from '@heroicons/react/24/outline';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface MCPTool {
  tool_name: string;
  description: string;
  business_value: string;
  complexity_level: 'low' | 'medium' | 'high';
  input_schema: Record<string, any>;
  output_schema: Record<string, any>;
  implementation_hints: string;
  use_cases: string[];
  dependencies: string[];
  error_scenarios: string[];
  estimated_effort_hours?: number;
}

interface MCPSuggestions {
  categories: Record<string, MCPTool[]>;
  prioritized_recommendations: Array<{
    priority: number;
    tool_name: string;
    justification: string;
    estimated_impact: 'high' | 'medium' | 'low';
    business_value_score: number;
  }>;
  implementation_roadmap: {
    phase_1_tools: string[];
    phase_2_tools: string[];
    phase_3_tools: string[];
  };
  total_tools_suggested: number;
  repository_analysis?: {
    complexity_score: number;
    primary_domain: string;
    key_capabilities: string[];
    technical_stack: string[];
  };
  tool_generation_strategy?: {
    total_tools_recommended: number;
    tool_distribution: Record<string, number>;
  };
}

interface SuggestionsData {
  analysis_id: number;
  suggestions_available: boolean;
  mcp_suggestions: MCPSuggestions;
  business_analysis: Record<string, any>;
  confidence_score: number;
  implementation_complexity: Record<string, any>;
}

const categoryIcons: Record<string, any> = {
  CORE_OPERATIONS: CpuChipIcon,
  DATA_MANAGEMENT: ServerIcon,
  INTEGRATION: Cog6ToothIcon,
  UTILITIES: CommandLineIcon,
  MONITORING: ChartBarIcon,
  AUTOMATION: SparklesIcon,
  REPORTING: DocumentTextIcon
};

const categoryColors: Record<string, string> = {
  CORE_OPERATIONS: 'bg-blue-100 text-blue-800',
  DATA_MANAGEMENT: 'bg-green-100 text-green-800',
  INTEGRATION: 'bg-purple-100 text-purple-800',
  UTILITIES: 'bg-orange-100 text-orange-800',
  MONITORING: 'bg-red-100 text-red-800',
  AUTOMATION: 'bg-yellow-100 text-yellow-800',
  REPORTING: 'bg-indigo-100 text-indigo-800'
};

export default function MCPSuggestionsPage() {
  const params = useParams();
  const router = useRouter();
  const analysisId = parseInt(params.id as string);

  const [suggestionsData, setSuggestionsData] = useState<SuggestionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [targetLanguage, setTargetLanguage] = useState('typescript');
  const [hostingArchitecture, setHostingArchitecture] = useState('http-sse');
  const [generationInProgress, setGenerationInProgress] = useState(false);
  const [downloadInProgress, setDownloadInProgress] = useState(false);
  const [generatedZipPath, setGeneratedZipPath] = useState<string | null>(null);
  const [generatedProjectStructure, setGeneratedProjectStructure] = useState<string[] | null>(null);
  const [setupInstructions, setSetupInstructions] = useState<string>('');
  const [activeTab, setActiveTab] = useState('overview');
  const [error, setError] = useState<string | null>(null);
  const [hasTriggeredEnhancement, setHasTriggeredEnhancement] = useState(false);
  const [pollCount, setPollCount] = useState(0);
  const [isRegenerating, setIsRegenerating] = useState(false);

  useEffect(() => {
    fetchSuggestions();
  }, [analysisId]);

  // Auto-trigger enhanced suggestions generation if not available (one time only)
  useEffect(() => {
    if (suggestionsData && !loading && !hasTriggeredEnhancement && suggestionsData.suggestions_available) {
      const hasEnhancedStructure = suggestionsData.mcp_suggestions.categories && 
        Object.keys(suggestionsData.mcp_suggestions.categories).some(cat => 
          ['CORE_OPERATIONS', 'DATA_MANAGEMENT', 'INTEGRATION', 'UTILITIES', 'MONITORING', 'AUTOMATION', 'REPORTING'].includes(cat)
        );
      
      const hasAnyTools = suggestionsData.mcp_suggestions.total_tools_suggested > 0;
      
      // Only trigger if we have suggestions available but they're empty or not enhanced
      if (!hasEnhancedStructure && !hasAnyTools && !isRegenerating) {
        console.log('Triggering enhanced suggestions generation once...');
        setHasTriggeredEnhancement(true);
        setIsRegenerating(true);
        fetchSuggestions(true);
      }
    }
  }, [suggestionsData, loading, hasTriggeredEnhancement]);

  const fetchSuggestions = async (regenerate = false) => {
    try {
      setLoading(true);
      setError(null);

      const data = await apiClient.getMCPSuggestions(analysisId, regenerate);
      
      console.log('MCP Suggestions Response:', data); // Debug log
      
      if (data.suggestions_available) {
        setSuggestionsData(data);
        setPollCount(0); // Reset poll count on success
        setIsRegenerating(false); // Reset regeneration flag
      } else if (pollCount < 12) { // Max 12 polls = 1 minute
        // Suggestions are being generated, poll for updates (without regenerate flag)
        console.log(`Suggestions not ready, will poll again in 5 seconds... (attempt ${pollCount + 1}/12)`);
        setPollCount(prev => prev + 1);
        setTimeout(() => fetchSuggestions(false), 5000); // Always poll without regenerate
      } else {
        console.log('Max polling attempts reached. Suggestions generation may have failed.');
        setError('Suggestion generation is taking longer than expected. Please refresh the page to try again.');
      }
    } catch (err: any) {
      console.error('Failed to fetch MCP suggestions:', err);
      
      // Handle specific error cases
      if (err.response?.status === 400) {
        // 400 error likely means regeneration is already in progress or GitHub token issue
        if (regenerate) {
          console.log('Regeneration request failed, will continue polling for existing task...');
          setIsRegenerating(false);
          // Continue polling without regenerate
          setTimeout(() => fetchSuggestions(false), 5000);
          return;
        }
      }
      
      setError('Failed to load MCP suggestions');
      setIsRegenerating(false);
    } finally {
      setLoading(false);
    }
  };

  const handleToolSelection = (toolName: string, selected: boolean) => {
    if (selected) {
      setSelectedTools([...selectedTools, toolName]);
    } else {
      setSelectedTools(selectedTools.filter(name => name !== toolName));
    }
  };

  const generateMCPServer = async () => {
    if (selectedTools.length === 0) return;

    setGenerationInProgress(true);
    setError(null);
    setGeneratedZipPath(null);
    setGeneratedProjectStructure(null);
    setSetupInstructions('');

    try {
      const response = await apiClient.generateMCPServer({
        analysis_id: analysisId,
        selected_tools: selectedTools,
        target_language: targetLanguage,
        customization_options: {
          include_tests: true,
          include_docker: true,
          authentication_method: 'api_key',
          hosting_type: hostingArchitecture
        }
      });

      if (response.success) {
        const zipPath = response.generation_result.zip_file_path;
        const projectStructure = response.generation_result.project_structure || [];
        const setupInstructions = response.generation_result.setup_instructions || '';

        setGeneratedZipPath(zipPath);
        setGeneratedProjectStructure(projectStructure);
        setSetupInstructions(setupInstructions);
      }
    } catch (err: any) {
      console.error('Failed to generate MCP server:', err);
      
      // Handle different types of errors
      if (err.code === 'ECONNABORTED') {
        setError('Generation is taking longer than expected. This is normal for complex repositories. Please wait...');
        // Optionally implement polling here
      } else if (err.response?.status === 500) {
        setError('Server error during generation. Please try again or contact support.');
      } else {
        setError(`Failed to generate MCP server: ${err.message || 'Unknown error'}`);
      }
    } finally {
      setGenerationInProgress(false);
    }
  };

  const downloadMCPServer = async () => {
    if (!generatedZipPath) return;

    setDownloadInProgress(true);
    try {
      const blob = await apiClient.downloadMCPServer(analysisId, generatedZipPath);
      
      // Create download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mcp-server-${Date.now()}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('Failed to download MCP server:', err);
      setError(`Failed to download MCP server: ${err.message || 'Unknown error'}`);
    } finally {
      setDownloadInProgress(false);
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSelectedToolsCount = () => {
    return selectedTools.length;
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Generating AI-powered MCP tool suggestions...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!suggestionsData) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No suggestions available</h3>
          <p className="mt-1 text-sm text-gray-500">
            MCP suggestions could not be generated for this analysis.
          </p>
          <div className="mt-6">
            <Button onClick={() => router.back()}>
              Go Back
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }


  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-1">MCP Tool Recommendations</h1>
                <p className="text-gray-600">
                  AI-generated tools for your repository
                </p>
              </div>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 mb-1">
                {suggestionsData.mcp_suggestions.total_tools_suggested}
              </p>
              <p className="text-sm text-gray-600">Tools Suggested</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">
              Overview
            </TabsTrigger>
            <TabsTrigger value="tools">
              Tool Selection
            </TabsTrigger>
            <TabsTrigger value="generate">
              Generate Server
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Business Analysis Summary */}
            <Card className="border border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <LightBulbIcon className="h-5 w-5 mr-2" />
                  Business Analysis Insights
                </CardTitle>
                <CardDescription>
                  Analysis of repository capabilities and structure
                </CardDescription>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-3 h-3 bg-indigo-500 rounded-full"></div>
                      <h4 className="font-semibold text-gray-900 text-lg">Primary Domain</h4>
                    </div>
                    <div className="bg-indigo-50 border border-indigo-100 rounded-xl p-6">
                      <p className="text-indigo-800 font-medium text-base leading-relaxed">
                        {suggestionsData.mcp_suggestions.repository_analysis?.primary_domain || 
                         suggestionsData.business_analysis.business_logic?.primary_domain || 
                         'Repository Analysis'}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <h4 className="font-semibold text-gray-900 text-lg">Business Purpose</h4>
                    </div>
                    <div className="bg-green-50 border border-green-100 rounded-xl p-6">
                      <p className="text-green-800 font-medium text-base leading-relaxed">
                        {suggestionsData.business_analysis.business_logic?.business_purpose || 
                         'Comprehensive repository analysis with AI-powered MCP tool generation'}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Quick Stats */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-indigo-600 mb-1">
                        {suggestionsData.mcp_suggestions.total_tools_suggested}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">Tools Available</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {Object.keys(suggestionsData.mcp_suggestions.categories).length}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">Categories</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-purple-600 mb-1">
                        {suggestionsData.mcp_suggestions.repository_analysis?.complexity_score || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">Complexity Score</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tools" className="space-y-8">
            {/* Selection Summary */}
            <Card className="border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CheckCircleIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Selection Summary</h3>
                      <p className="text-sm text-gray-600">Choose tools to include in your MCP server</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-gray-900">{selectedTools.length}</div>
                    <div className="text-sm text-gray-500">Selected Tools</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tool Categories */}
            {Object.entries(suggestionsData.mcp_suggestions.categories).map(([category, tools]) => {
              if (!tools || tools.length === 0) return null;
              
              const IconComponent = categoryIcons[category] || CpuChipIcon;
              
              return (
                <Card key={category} className="border-0 shadow-lg shadow-gray-100/50 overflow-hidden">
                  <CardHeader className={`bg-gradient-to-r ${
                    category === 'CORE_OPERATIONS' ? 'from-blue-500 to-cyan-500' :
                    category === 'DATA_MANAGEMENT' ? 'from-green-500 to-emerald-500' :
                    category === 'INTEGRATION' ? 'from-purple-500 to-violet-500' :
                    category === 'UTILITIES' ? 'from-orange-500 to-red-500' :
                    category === 'MONITORING' ? 'from-red-500 to-pink-500' :
                    category === 'AUTOMATION' ? 'from-yellow-500 to-orange-500' :
                    category === 'REPORTING' ? 'from-indigo-500 to-purple-500' :
                    'from-gray-500 to-slate-500'
                  } text-white relative`}>
                    <div className="absolute inset-0 bg-black/10"></div>
                    <CardTitle className="flex items-center text-xl font-bold relative z-10">
                      <IconComponent className="h-6 w-6 mr-3" />
                      {category.replace(/_/g, ' ')}
                      <div className="ml-auto bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-semibold">
                        {tools.length} tools
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid gap-4">
                      {tools.map((tool) => (
                        <div key={tool.tool_name} className="group border border-gray-100 rounded-xl p-5 hover:shadow-lg hover:shadow-gray-100/50 transition-all duration-200 bg-gradient-to-r from-white to-gray-50/30">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4 flex-1">
                              <Checkbox
                                checked={selectedTools.includes(tool.tool_name)}
                                onCheckedChange={(checked) => 
                                  handleToolSelection(tool.tool_name, checked as boolean)
                                }
                                className="mt-1 data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600"
                              />
                              <div className="flex-1">
                                <h4 className="font-semibold text-gray-900 text-lg mb-2">
                                  {tool.tool_name.replace(/_/g, ' ')}
                                </h4>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                  {tool.description || tool.business_value || 'AI-generated MCP tool'}
                                </p>
                                <div className="flex items-center space-x-4 mb-3">
                                  <Badge className={`${getComplexityColor(tool.complexity_level)} font-semibold`}>
                                    {tool.complexity_level} complexity
                                  </Badge>
                                </div>
                                {tool.use_cases && tool.use_cases.length > 0 && (
                                  <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
                                    <p className="text-xs font-semibold text-gray-700 mb-2">Use cases:</p>
                                    <ul className="space-y-1">
                                      {(tool.use_cases || []).slice(0, 3).map((useCase, idx) => (
                                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                                          <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mr-2 flex-shrink-0"></div>
                                          {useCase}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>

          <TabsContent value="generate" className="space-y-8">
            <Card className="border-0 bg-gradient-to-br from-white via-white to-indigo-50/30 shadow-xl shadow-indigo-100/50 overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white relative">
                <div className="absolute inset-0 bg-black/10"></div>
                <CardTitle className="flex items-center text-2xl font-bold relative z-10">
                  <RocketLaunchIcon className="h-7 w-7 mr-3" />
                  Generate MCP Server
                </CardTitle>
                <CardDescription className="text-indigo-100 text-lg relative z-10">
                  Create a production-ready MCP server with your selected tools
                </CardDescription>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                {/* Selection Summary */}
                <div className="bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 rounded-2xl p-6 border border-indigo-100">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <CheckCircleIcon className="h-5 w-5 text-white" />
                    </div>
                    <h4 className="font-semibold text-gray-900 text-lg">Selection Summary</h4>
                  </div>
                  <div className="grid grid-cols-1 gap-6">
                    <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 text-center">
                      <div className="text-2xl font-bold text-indigo-600 mb-1">{selectedTools.length}</div>
                      <div className="text-sm text-gray-600 font-medium">Selected Tools</div>
                    </div>
                  </div>
                </div>

                {/* Language Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Language
                    </label>
                    <Select value={targetLanguage} onValueChange={setTargetLanguage}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="typescript">
                          <div className="flex flex-col">
                            <span className="font-medium">TypeScript</span>
                            <span className="text-xs text-gray-500">Most common, best tooling</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="javascript">
                          <div className="flex flex-col">
                            <span className="font-medium">JavaScript</span>
                            <span className="text-xs text-gray-500">Quick prototyping</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="python">
                          <div className="flex flex-col">
                            <span className="font-medium">Python</span>
                            <span className="text-xs text-gray-500">Direct wrapper around existing code</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="go">
                          <div className="flex flex-col">
                            <span className="font-medium">Go</span>
                            <span className="text-xs text-gray-500">Excellent for high-performance hosting</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="rust">
                          <div className="flex flex-col">
                            <span className="font-medium">Rust</span>
                            <span className="text-xs text-gray-500">Maximum performance + safety</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="java">
                          <div className="flex flex-col">
                            <span className="font-medium">Java</span>
                            <span className="text-xs text-gray-500">Enterprise environments</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="csharp">
                          <div className="flex flex-col">
                            <span className="font-medium">C#/.NET</span>
                            <span className="text-xs text-gray-500">Windows-heavy environments</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hosting Architecture
                    </label>
                    <Select value={hostingArchitecture} onValueChange={setHostingArchitecture}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="http-sse">
                          <div className="flex flex-col">
                            <span className="font-medium">HTTP/SSE Server</span>
                            <span className="text-xs text-gray-500">Recommended - Remote hosting</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="websocket">
                          <div className="flex flex-col">
                            <span className="font-medium">WebSocket Server</span>
                            <span className="text-xs text-gray-500">Real-time bidirectional</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="stdio">
                          <div className="flex flex-col">
                            <span className="font-medium">Local STDIO</span>
                            <span className="text-xs text-gray-500">Direct process communication</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="docker">
                          <div className="flex flex-col">
                            <span className="font-medium">Docker Container</span>
                            <span className="text-xs text-gray-500">Isolated deployment</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Architecture Description */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">
                    {hostingArchitecture === 'http-sse' && 'HTTP/SSE Server Architecture'}
                    {hostingArchitecture === 'websocket' && 'WebSocket Server Architecture'}
                    {hostingArchitecture === 'stdio' && 'Local STDIO Architecture'}
                    {hostingArchitecture === 'docker' && 'Docker Container Architecture'}
                  </h4>
                  <p className="text-sm text-blue-700">
                    {hostingArchitecture === 'http-sse' && 'Remote server that communicates via HTTP requests and Server-Sent Events. Best for cloud deployment and scaling. Supports multiple concurrent clients.'}
                    {hostingArchitecture === 'websocket' && 'Real-time bidirectional communication over WebSocket protocol. Ideal for interactive applications requiring low latency.'}
                    {hostingArchitecture === 'stdio' && 'Direct process communication through standard input/output. Fastest option for local usage and development.'}
                    {hostingArchitecture === 'docker' && 'Containerized deployment with isolated environment. Perfect for microservices and cloud-native architectures.'}
                  </p>
                </div>

                {/* Generated Files Structure */}
                {generatedProjectStructure && generatedProjectStructure.length > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
                      <h4 className="font-medium text-green-900">Generation Complete!</h4>
                    </div>
                    <p className="text-sm text-green-700 mb-4">
                      Your MCP server has been generated successfully. Review the file structure below:
                    </p>
                    
                    <div className="bg-white rounded-lg border p-3 max-h-60 overflow-y-auto">
                      <div className="space-y-1">
                        {generatedProjectStructure.map((file, index) => {
                          const isFolder = file.endsWith('/') || (!file.includes('.') && file.includes('/'));
                          const fileName = file.split('/').pop() || file;
                          const depth = (file.match(/\//g) || []).length;
                          
                          return (
                            <div 
                              key={index} 
                              className="flex items-center space-x-2 text-sm py-0.5"
                              style={{ paddingLeft: `${depth * 16}px` }}
                            >
                              {isFolder ? (
                                <FolderIcon className="h-3 w-3 text-blue-600 flex-shrink-0" />
                              ) : (
                                <DocumentIcon className="h-3 w-3 text-gray-600 flex-shrink-0" />
                              )}
                              <span className={isFolder ? "font-medium text-gray-900" : "text-gray-700"}>
                                {fileName}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    
                    {setupInstructions && (
                      <div className="mt-3 bg-white rounded-lg border p-3">
                        <h5 className="font-medium text-gray-900 mb-2">Setup Instructions</h5>
                        <pre className="text-xs text-gray-600 whitespace-pre-wrap max-h-24 overflow-y-auto">
                          {setupInstructions}
                        </pre>
                      </div>
                    )}
                  </div>
                )}

                {/* Generate and Download Buttons */}
                <div className="pt-4 border-t space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      {selectedTools.length === 0 && "Select at least one tool to generate server"}
                      {generatedZipPath && "Ready to download your MCP server"}
                      {generationInProgress && "Generation in progress... This may take 1-3 minutes for complex repositories."}
                    </div>
                    <Button
                      onClick={generateMCPServer}
                      disabled={selectedTools.length === 0 || generationInProgress}
                      className="bg-indigo-600 hover:bg-indigo-700"
                    >
                      {generationInProgress ? (
                        <>
                          <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <RocketLaunchIcon className="h-4 w-4 mr-2" />
                          Generate Server
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {generationInProgress && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-blue-600 mr-2 animate-spin" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-900">
                            Generating your MCP server...
                          </h4>
                          <p className="text-xs text-blue-700 mt-1">
                            This process typically takes 1-3 minutes. We&apos;re analyzing your repository, 
                            generating tools, and creating a complete MCP server package.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {generatedZipPath && (
                    <div className="flex justify-end">
                      <Button
                        onClick={downloadMCPServer}
                        disabled={downloadInProgress}
                        variant="outline"
                        className="border-green-600 text-green-600 hover:bg-green-50"
                      >
                        {downloadInProgress ? (
                          <>
                            <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                            Downloading...
                          </>
                        ) : (
                          <>
                            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                            Download Server
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}