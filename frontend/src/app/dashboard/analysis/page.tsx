'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ChartBarIcon, 
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CpuChipIcon,
  CodeBracketIcon,
  SparklesIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import apiClient, { Analysis, AnalysisStats } from '@/lib/api';

export default function AnalysisPage() {
  const [analyses, setAnalyses] = useState<Analysis[]>([]);
  const [filteredAnalyses, setFilteredAnalyses] = useState<Analysis[]>([]);
  const [stats, setStats] = useState<AnalysisStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [scoreFilter, setScoreFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalyses();
  }, []);

  useEffect(() => {
    filterAnalyses();
  }, [analyses, searchTerm, statusFilter, scoreFilter, sortBy]);

  const fetchAnalyses = async () => {
    try {
      const [analysesData, statsData] = await Promise.all([
        apiClient.getAnalysisHistory(0, 100),
        apiClient.getAnalysisStats()
      ]);
      setAnalyses(analysesData);
      setStats(statsData);
    } catch (err) {
      console.error('Failed to fetch analyses:', err);
      setError('Failed to load analyses');
    } finally {
      setLoading(false);
    }
  };

  const filterAnalyses = () => {
    let filtered = analyses;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(analysis => 
        analysis.repo_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        analysis.repo_owner.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(analysis => analysis.status === statusFilter);
    }

    // Score filter
    if (scoreFilter !== 'all') {
      filtered = filtered.filter(analysis => {
        if (!analysis.mcp_feasibility_score) return false;
        const score = analysis.mcp_feasibility_score;
        switch (scoreFilter) {
          case 'high': return score >= 75;
          case 'medium': return score >= 50 && score < 75;
          case 'low': return score < 50;
          default: return true;
        }
      });
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'created_at':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'repo_name':
          return `${a.repo_owner}/${a.repo_name}`.localeCompare(`${b.repo_owner}/${b.repo_name}`);
        case 'mcp_score':
          return (b.mcp_feasibility_score || 0) - (a.mcp_feasibility_score || 0);
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    setFilteredAnalyses(filtered);
  };

  const handleRetry = async (analysisId: number) => {
    try {
      await apiClient.retryAnalysis(analysisId);
      // Refresh the list after retry
      setTimeout(() => fetchAnalyses(), 1000);
    } catch (err) {
      console.error('Failed to retry analysis:', err);
    }
  };

  const handleDelete = async (analysisId: number) => {
    try {
      await apiClient.deleteAnalysis(analysisId);
      setAnalyses(analyses.filter(a => a.id !== analysisId));
    } catch (err) {
      console.error('Failed to delete analysis:', err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'analyzing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'success',
      failed: 'destructive',
      analyzing: 'default',
      pending: 'secondary'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  const getMCPScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 75) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Modern Header */}
        <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 shadow-2xl shadow-indigo-500/25">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-white mb-3">Analysis Dashboard</h1>
                <p className="text-xl text-indigo-100 leading-relaxed">
                  AI-powered repository analysis with enhanced multi-dimensional framework
                </p>
              </div>
              <Link href="/dashboard/analysis/new">
                <Button className="bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-200 px-6 py-3 shadow-lg">
                  <PlusIcon className="mr-2 h-5 w-5" />
                  New Analysis
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-xl shadow-blue-100/50 hover:shadow-2xl hover:shadow-blue-100/50 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-600 mb-2">Total Analyses</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{stats.total_analyses}</p>
                    <p className="text-sm text-gray-500 mt-1">All repository analyses</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <ChartBarIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-white via-white to-green-50/30 shadow-xl shadow-green-100/50 hover:shadow-2xl hover:shadow-green-100/50 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-600 mb-2">Completed</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">{stats.completed_analyses}</p>
                    <p className="text-sm text-gray-500 mt-1">Successfully analyzed</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <CheckCircleIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-white via-white to-purple-50/30 shadow-xl shadow-purple-100/50 hover:shadow-2xl hover:shadow-purple-100/50 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-600 mb-2">In Progress</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">{stats.pending_analyses}</p>
                    <p className="text-sm text-gray-500 mt-1">Currently processing</p>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <ClockIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Filters */}
        <Card className="border-0 bg-gradient-to-br from-white via-white to-gray-50/30 shadow-xl shadow-gray-100/50">
          <CardHeader className="bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-t-lg relative">
            <div className="absolute inset-0 bg-black/10"></div>
            <CardTitle className="text-xl flex items-center relative z-10">
              <FunnelIcon className="mr-3 h-6 w-6" />
              Filters & Sorting
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-3">
                <label className="text-sm font-semibold text-gray-700">Search repositories</label>
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Search by repository name or owner..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>
              </div>
              
              <div className="space-y-3">
                <label className="text-sm font-semibold text-gray-700">Status</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="border-gray-200 focus:border-indigo-500 focus:ring-indigo-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="analyzing">Analyzing</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <label className="text-sm font-semibold text-gray-700">MCP Score</label>
                <Select value={scoreFilter} onValueChange={setScoreFilter}>
                  <SelectTrigger className="border-gray-200 focus:border-indigo-500 focus:ring-indigo-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All scores</SelectItem>
                    <SelectItem value="high">High (75-100)</SelectItem>
                    <SelectItem value="medium">Medium (50-74)</SelectItem>
                    <SelectItem value="low">Low (0-49)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <label className="text-sm font-semibold text-gray-700">Sort by</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="border-gray-200 focus:border-indigo-500 focus:ring-indigo-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Latest first</SelectItem>
                    <SelectItem value="mcp_score">MCP Score</SelectItem>
                    <SelectItem value="repo_name">Repository name</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Results */}
        {filteredAnalyses.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredAnalyses.map((analysis) => (
              <Card key={analysis.id} className="group hover:shadow-xl hover:shadow-indigo-100/50 transition-all duration-300 border-0 bg-gradient-to-br from-white via-white to-gray-50/30 hover:scale-[1.02]">
                <CardContent className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(analysis.status)}
                      <div>
                        <Link 
                          href={`/dashboard/analysis/${analysis.id}`}
                          className="text-lg font-semibold text-gray-900 hover:text-indigo-600 transition-colors duration-200 flex items-center group/link"
                        >
                          {analysis.repo_owner}/{analysis.repo_name}
                          <ArrowTopRightOnSquareIcon className="ml-2 h-4 w-4 opacity-0 group-hover/link:opacity-100 transition-opacity duration-200" />
                        </Link>
                        <p className="text-sm text-gray-500 mt-1">
                          {new Date(analysis.created_at).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                          {analysis.completed_at && (
                            <span className="ml-2 text-green-600">
                              • Completed {new Date(analysis.completed_at).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric'
                              })}
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                    {getStatusBadge(analysis.status)}
                  </div>

                  {/* MCP Score */}
                  {analysis.mcp_feasibility_score && (
                    <div className="mb-4">
                      <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${
                        analysis.mcp_feasibility_score >= 75 
                          ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/25' 
                          : analysis.mcp_feasibility_score >= 50 
                          ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-lg shadow-yellow-400/25' 
                          : 'bg-gradient-to-r from-red-400 to-pink-400 text-white shadow-lg shadow-red-400/25'
                      }`}>
                        <CpuChipIcon className="h-4 w-4 mr-2" />
                        {Math.round(analysis.mcp_feasibility_score)} MCP Score
                      </div>
                    </div>
                  )}

                  {/* Analysis Summary */}
                  {analysis.status === 'completed' && (
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-3">
                        {analysis.analysis_results?.api_endpoints?.length > 0 && (
                          <div className="flex items-center space-x-2 p-2 rounded-lg bg-blue-50 border border-blue-100">
                            <CodeBracketIcon className="h-4 w-4 text-blue-600" />
                            <span className="text-sm text-blue-700 font-medium">
                              {analysis.analysis_results.api_endpoints.length} API Endpoints
                            </span>
                          </div>
                        )}
                        {analysis.analysis_results?.dependencies?.total_dependencies > 0 && (
                          <div className="flex items-center space-x-2 p-2 rounded-lg bg-purple-50 border border-purple-100">
                            <CpuChipIcon className="h-4 w-4 text-purple-600" />
                            <span className="text-sm text-purple-700 font-medium">
                              {analysis.analysis_results.dependencies.total_dependencies} Dependencies
                            </span>
                          </div>
                        )}
                        {analysis.analysis_results?.code_patterns?.suggested_tools?.length > 0 && (
                          <div className="flex items-center space-x-2 p-2 rounded-lg bg-green-50 border border-green-100">
                            <SparklesIcon className="h-4 w-4 text-green-600" />
                            <span className="text-sm text-green-700 font-medium">
                              {analysis.analysis_results.code_patterns.suggested_tools.length} Code Patterns
                            </span>
                          </div>
                        )}
                        {Object.keys(analysis.analysis_results?.languages || {}).length > 0 && (
                          <div className="flex items-center space-x-2 p-2 rounded-lg bg-indigo-50 border border-indigo-100">
                            <ChartBarIcon className="h-4 w-4 text-indigo-600" />
                            <span className="text-sm text-indigo-700 font-medium">
                              {Object.keys(analysis.analysis_results.languages).length} Languages
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <Link href={`/dashboard/analysis/${analysis.id}`}>
                      <Button variant="ghost" size="sm" className="text-gray-600 hover:text-indigo-600 hover:bg-indigo-50">
                        <EyeIcon className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </Link>
                    
                    <div className="flex space-x-2">
                      {analysis.status === 'failed' && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleRetry(analysis.id)}
                          className="border-orange-200 text-orange-600 hover:bg-orange-50"
                        >
                          <ArrowPathIcon className="h-4 w-4 mr-1" />
                          Retry
                        </Button>
                      )}
                      
                      {analysis.status === 'completed' && analysis.mcp_feasibility_score && analysis.mcp_feasibility_score >= 50 && (
                        <Link href={`/dashboard/analysis/${analysis.id}/mcp-suggestions`}>
                          <Button size="sm" className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 shadow-lg shadow-indigo-500/25">
                            <CpuChipIcon className="h-4 w-4 mr-2" />
                            Generate MCP
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>

                  {analysis.error_message && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-700">{analysis.error_message}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="border-0 bg-gradient-to-br from-white via-white to-indigo-50/30 shadow-xl shadow-indigo-100/50">
            <CardContent className="text-center py-16">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <ChartBarIcon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {analyses.length === 0 ? 'Ready to Start Analyzing?' : 'No analyses match your filters'}
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                {analyses.length === 0 
                  ? 'Transform your GitHub repositories into powerful MCP servers with AI-powered analysis.'
                  : 'Try adjusting your search terms or filters to find what you\'re looking for.'
                }
              </p>
              {analyses.length === 0 && (
                <Link href="/dashboard/analysis/new">
                  <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg shadow-indigo-500/25 px-8 py-3">
                    <PlusIcon className="mr-2 h-5 w-5" />
                    Analyze Your First Repository
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}

        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex">
                <XCircleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}