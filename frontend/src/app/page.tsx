'use client'

import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Github, 
  Zap, 
  Shield, 
  Brain, 
  Code, 
  Sparkles,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Rocket,
  Target,
  BarChart3,
  GitBranch,
  Cpu,
  Database,
  Network,
  ChevronRight
} from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'

export default function Home() {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, loading, router])

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading SuperMCP...</p>
        </div>
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-white overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div 
          className="absolute inset-0 opacity-30"
          style={{
            background: `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(79, 70, 229, 0.1), transparent 40%)`
          }}
        />
      </div>
      
      {/* Navigation */}
      <nav className="relative z-10 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-tr from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              SuperMCP
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/auth/login">
              <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                Sign In
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg">
                <Github className="w-4 h-4 mr-2" />
                Get Started
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 px-6 pt-20 pb-32">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8 animate-fade-up">
            <Badge className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border-indigo-200 px-4 py-2 mb-6 animate-bounce-gentle">
              <Sparkles className="w-4 h-4 mr-2" />
              AI-Powered Repository Analysis
            </Badge>
          </div>
          
          <h1 className="text-6xl md:text-7xl font-bold mb-8 leading-tight animate-fade-up" style={{ animationDelay: '0.2s' }}>
            <span className="bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 bg-clip-text text-transparent">
              Transform Repositories
            </span>
            <br />
            <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Into MCP Servers
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-up" style={{ animationDelay: '0.4s' }}>
            Leverage cutting-edge AI to analyze GitHub repositories across 7+ languages, discover MCP server potential, 
            and automatically generate production-ready Model Context Protocol implementations in your preferred language.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-up" style={{ animationDelay: '0.6s' }}>
            <Link href="/auth/login">
              <Button size="lg" className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200 px-8 py-4 animate-glow">
                <Github className="w-5 h-5 mr-2" />
                Start Analysis
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="border-2 border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 px-8 py-4 hover:scale-105 transition-all duration-200">
              <Rocket className="w-5 h-5 mr-2" />
              View Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-fade-up" style={{ animationDelay: '0.8s' }}>
            <div className="text-center group">
              <div className="text-3xl font-bold text-indigo-600 mb-2 group-hover:scale-110 transition-transform duration-300">10K+</div>
              <div className="text-gray-600">Repositories Analyzed</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl font-bold text-purple-600 mb-2 group-hover:scale-110 transition-transform duration-300">95%</div>
              <div className="text-gray-600">Accuracy Rate</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl font-bold text-green-600 mb-2 group-hover:scale-110 transition-transform duration-300">2.3s</div>
              <div className="text-gray-600">Average Analysis Time</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 px-6 py-24 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent">
                Universal Language Support
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Analyze repositories in any language, generate MCP servers in your preferred tech stack
            </p>
            
            {/* Language Support Grid */}
            <div className="grid grid-cols-3 md:grid-cols-7 gap-4 max-w-4xl mx-auto mb-16">
              {[
                { name: 'Python', color: 'from-blue-500 to-green-500' },
                { name: 'TypeScript', color: 'from-blue-600 to-blue-400' },
                { name: 'JavaScript', color: 'from-yellow-400 to-orange-500' },
                { name: 'Go', color: 'from-cyan-500 to-blue-500' },
                { name: 'Rust', color: 'from-orange-600 to-red-500' },
                { name: 'Java', color: 'from-red-600 to-orange-500' },
                { name: 'C#', color: 'from-purple-600 to-indigo-500' }
              ].map((lang, index) => (
                <div key={lang.name} className="text-center group">
                  <div className={`h-12 w-12 bg-gradient-to-r ${lang.color} rounded-xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <Code className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-xs font-medium text-gray-700">{lang.name}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-20">
            <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg hover:scale-105">
              <CardHeader className="pb-4">
                <div className="h-12 w-12 bg-gradient-to-tr from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-xl mb-2">Comprehensive Repository Analysis</CardTitle>
                <CardDescription className="text-gray-600">
                  Deep AI analysis of code structure across 7+ languages, business logic patterns, API capabilities, and integration opportunities to identify high-value MCP server potential.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Multi-language analysis (Python, JS, TS, Go, Java, C#, Rust)
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Cross-language dependency analysis
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Business logic & workflow extraction
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    API patterns & integration analysis
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg hover:scale-105">
              <CardHeader className="pb-4">
                <div className="h-12 w-12 bg-gradient-to-tr from-green-500 to-blue-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-xl mb-2">Real-time Processing</CardTitle>
                <CardDescription className="text-gray-600">
                  Lightning-fast analysis with live progress tracking and comprehensive reporting for immediate insights.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Live analysis progress
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Detailed implementation roadmap
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Priority-based recommendations
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg hover:scale-105">
              <CardHeader className="pb-4">
                <div className="h-12 w-12 bg-gradient-to-tr from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Code className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-xl mb-2">Multi-Language Code Generation</CardTitle>
                <CardDescription className="text-gray-600">
                  Generate production-ready MCP servers in Python, TypeScript, Go, Rust, Java, or C# with comprehensive documentation, testing, and deployment configurations.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Multi-language MCP servers (Python, TypeScript, Go, Rust, Java, C#)
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Language-native testing frameworks
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Docker & cloud deployment configurations
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    Best practices for each language ecosystem
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Process Steps */}
          <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-3xl p-12">
            <div className="text-center mb-16">
              <h3 className="text-3xl font-bold mb-4 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                How It Works
              </h3>
              <p className="text-gray-600 text-lg">
                Transform your repository into a powerful MCP server in just a few steps
              </p>
            </div>

            <div className="grid md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="h-16 w-16 bg-gradient-to-tr from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Github className="h-8 w-8 text-white" />
                </div>
                <h4 className="font-semibold text-lg mb-3">1. Connect Repository</h4>
                <p className="text-gray-600 text-sm">
                  Securely connect your GitHub repository with OAuth authentication
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-gradient-to-tr from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <h4 className="font-semibold text-lg mb-3">2. AI Analysis</h4>
                <p className="text-gray-600 text-sm">
                  Our AI analyzes your code structure, dependencies, and business logic
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-gradient-to-tr from-green-600 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <h4 className="font-semibold text-lg mb-3">3. Tool Suggestions</h4>
                <p className="text-gray-600 text-sm">
                  Receive categorized MCP tool recommendations with implementation roadmap
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-gradient-to-tr from-orange-600 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Rocket className="h-8 w-8 text-white" />
                </div>
                <h4 className="font-semibold text-lg mb-3">4. Generate Server</h4>
                <p className="text-gray-600 text-sm">
                  Download production-ready MCP server code with documentation
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="relative z-10 px-6 py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-3xl font-bold mb-4 bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent">
            Built with Modern Technology
          </h3>
          <p className="text-gray-600 text-lg mb-16">
            Powered by cutting-edge AI and robust infrastructure
          </p>

          <div className="grid grid-cols-2 md:grid-cols-6 gap-8">
            <div className="flex flex-col items-center group">
              <div className="h-16 w-16 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Brain className="h-8 w-8 text-indigo-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">Claude AI</span>
            </div>

            <div className="flex flex-col items-center group">
              <div className="h-16 w-16 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Code className="h-8 w-8 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">TypeScript</span>
            </div>

            <div className="flex flex-col items-center group">
              <div className="h-16 w-16 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Cpu className="h-8 w-8 text-green-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">Next.js</span>
            </div>

            <div className="flex flex-col items-center group">
              <div className="h-16 w-16 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Database className="h-8 w-8 text-purple-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">PostgreSQL</span>
            </div>

            <div className="flex flex-col items-center group">
              <div className="h-16 w-16 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Network className="h-8 w-8 text-orange-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">FastAPI</span>
            </div>

            <div className="flex flex-col items-center group">
              <div className="h-16 w-16 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Shield className="h-8 w-8 text-red-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">OAuth</span>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-6 py-24">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-indigo-600 to-purple-600 rounded-3xl p-12 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-black opacity-10"></div>
            <div className="relative z-10">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ready to Transform Your Repository?
              </h2>
              <p className="text-xl mb-8 opacity-90">
                Join developers who are already leveraging AI to build powerful MCP servers. 
                Start your analysis in seconds.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/login">
                  <Button size="lg" className="bg-white text-indigo-600 hover:bg-gray-100 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200 px-8 py-4">
                    <Github className="w-5 h-5 mr-2" />
                    Start Free Analysis
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
                <Button variant="outline" size="lg" className="border-2 border-white text-white hover:bg-white hover:text-indigo-600 px-8 py-4">
                  <Users className="w-5 h-5 mr-2" />
                  Join Community
                </Button>
              </div>

              <div className="mt-8 flex items-center justify-center space-x-6 text-sm opacity-75">
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Free to start
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  No credit card required
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Instant results
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 px-6 py-12 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="h-8 w-8 bg-gradient-to-tr from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">SuperMCP</span>
            </div>
            
            <div className="flex items-center space-x-6 text-gray-400">
              <Link href="#" className="hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="#" className="hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="#" className="hover:text-white transition-colors">
                Documentation
              </Link>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 SuperMCP. All rights reserved. Built with AI for the future of development.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}