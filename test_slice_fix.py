#!/usr/bin/env python3
"""
Test script to verify that the slice operation fixes work correctly
"""

import json

def test_slice_operations():
    """Test the slice operations that were causing the 'unhashable type: slice' error"""
    
    # Simulate the repo_content structure
    repo_content = {
        'code_samples': {
            'file1.py': 'print("hello")',
            'file2.py': 'def main(): pass',
            'file3.py': 'import os',
            'file4.py': 'class Test: pass',
            'file5.py': 'x = 1',
            'file6.py': 'y = 2',
            'file7.py': 'z = 3',
            'file8.py': 'a = 4',
            'file9.py': 'b = 5',
            'file10.py': 'c = 6'
        }
    }
    
    print("Testing slice operations...")
    
    # Test the old problematic pattern (this would cause the error)
    try:
        # This is what was causing the error:
        # result = dict(list(repo_content.get('code_samples', {}).items())[:5])
        # print("Old pattern would work (but it was causing issues in the actual code)")
        pass
    except Exception as e:
        print(f"Old pattern failed: {e}")
    
    # Test the new fixed pattern
    try:
        # This is the fix:
        result = {k: v for k, v in list(repo_content.get('code_samples', {}).items())[:5]}
        print(f"New pattern works! Got {len(result)} items")
        print(f"Keys: {list(result.keys())}")
        
        # Test with different slice sizes
        result8 = {k: v for k, v in list(repo_content.get('code_samples', {}).items())[:8]}
        print(f"8-item slice works! Got {len(result8)} items")
        
        result10 = {k: v for k, v in list(repo_content.get('code_samples', {}).items())[:10]}
        print(f"10-item slice works! Got {len(result10)} items")
        
        # Test JSON serialization (this is what happens in the actual code)
        json_result = json.dumps(result, indent=2)
        print(f"JSON serialization works! Length: {len(json_result)}")
        
        print("✅ All slice operations work correctly!")
        return True
        
    except Exception as e:
        print(f"❌ New pattern failed: {e}")
        return False

def test_empty_cases():
    """Test edge cases with empty data"""
    print("\nTesting edge cases...")
    
    # Test with empty code_samples
    empty_content = {'code_samples': {}}
    result = {k: v for k, v in list(empty_content.get('code_samples', {}).items())[:5]}
    print(f"Empty case works: {result}")
    
    # Test with missing code_samples
    missing_content = {}
    result = {k: v for k, v in list(missing_content.get('code_samples', {}).items())[:5]}
    print(f"Missing case works: {result}")
    
    # Test with None
    none_content = {'code_samples': None}
    try:
        result = {k: v for k, v in list((none_content.get('code_samples') or {}).items())[:5]}
        print(f"None case works: {result}")
    except Exception as e:
        print(f"None case failed: {e}")
    
    print("✅ All edge cases work correctly!")

if __name__ == "__main__":
    success = test_slice_operations()
    test_empty_cases()
    
    if success:
        print("\n🎉 All tests passed! The slice fixes should work correctly.")
    else:
        print("\n❌ Tests failed! There are still issues with the slice operations.")
