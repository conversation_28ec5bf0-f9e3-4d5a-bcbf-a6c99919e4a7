# GitHub OAuth Setup Guide

## Quick Setup Steps

### 1. Create GitHub OAuth App

1. Go to **GitHub.com** → **Settings** → **Developer settings** → **OAuth Apps**
2. Click **"New OAuth App"** 
3. Fill out the form:
   - **Application name**: `SuperMCP Development`
   - **Homepage URL**: `http://localhost:3000`
   - **Authorization callback URL**: `http://localhost:3000/auth/callback`
4. Click **"Register application"**
5. Copy the **Client ID** and **Client Secret**

### 2. Update Environment Variables

Edit the `.env` file in the project root:

```bash
# Replace these lines with your actual GitHub OAuth credentials:
GITHUB_CLIENT_ID=your_actual_client_id_here
GITHUB_CLIENT_SECRET=your_actual_client_secret_here
```

### 3. Restart Docker Containers

```bash
docker-compose -f docker-compose.dev.yml restart
```

### 4. Test Authentication

1. Go to http://localhost:3000
2. Click "Sign in with GitHub"
3. You should be redirected to GitHub for authorization
4. After approval, you'll be redirected back to SuperMCP

## Troubleshooting

**If you see "your_github_client_id" in the URL:**
- The environment variables haven't been updated
- Make sure to restart the containers after updating .env

**If you get "404 This site can't be reached":**
- Check that the callback URL in GitHub matches: `http://localhost:3000/auth/callback`
- Ensure containers are running: `docker ps`

**If authentication fails:**
- Check backend logs: `docker logs supermcp-backend`
- Verify the client secret is correct