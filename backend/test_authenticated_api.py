#!/usr/bin/env python3
"""
Test authenticated API call to simulate frontend behavior
"""
import requests
import json
from app.utils.auth import create_access_token
from app.database import get_db
from app.models import User

def test_authenticated_api():
    # Get user from database to create a valid token
    db = next(get_db())
    user = db.query(User).filter(User.id == 1).first()
    
    if not user:
        print("User not found")
        return
    
    # Create JWT token for the user
    token = create_access_token(data={"sub": str(user.id)})
    print(f"Generated token for user: {user.username}")
    
    # Test the API call with authentication
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n=== Testing authenticated API call ===")
    response = requests.get(
        "http://localhost:8000/api/v1/mcp-generate/1/suggestions",
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"\n=== API Response Analysis ===")
        print(f"suggestions_available: {data.get('suggestions_available')}")
        print(f"analysis_id: {data.get('analysis_id')}")
        
        if 'mcp_suggestions' in data:
            mcp_suggestions = data['mcp_suggestions']
            print(f"mcp_suggestions keys: {list(mcp_suggestions.keys())}")
            
            if 'categories' in mcp_suggestions:
                categories = mcp_suggestions['categories']
                print(f"Categories found: {list(categories.keys())}")
                
                # Check each category
                for cat_name, tools in categories.items():
                    print(f"  {cat_name}: {len(tools)} tools")
                    if tools:
                        tool = tools[0] # Print first tool as example
                        print(f"    Sample tool: {tool.get('tool_name', 'N/A')}")
                        print(f"    Sample tool keys: {list(tool.keys())}")
            
            print(f"total_tools_suggested: {mcp_suggestions.get('total_tools_suggested', 'N/A')}")
        
        print(f"confidence_score: {data.get('confidence_score', 'N/A')}")
        
        # Check if this matches what frontend expects
        print(f"\n=== Frontend Compatibility Check ===")
        frontend_expects = {
            'suggestions_available': bool,
            'mcp_suggestions': dict,
            'business_analysis': dict,
            'confidence_score': (int, float),
            'implementation_complexity': dict
        }
        
        for key, expected_type in frontend_expects.items():
            if key in data:
                actual_type = type(data[key])
                compatible = isinstance(data[key], expected_type)
                print(f"✅ {key}: {actual_type.__name__} (compatible: {compatible})")
            else:
                print(f"❌ {key}: MISSING")
    else:
        print(f"Error: {response.text}")
    
    db.close()

if __name__ == "__main__":
    test_authenticated_api()