#!/usr/bin/env python3
"""
Debug the exact data structure in the database
"""
import json
from app.database import get_db
from app.models import RepoAnalysis

def debug_data_structure():
    db = next(get_db())
    analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == 22).first()
    
    if analysis and analysis.analysis_results:
        results = analysis.analysis_results
        ai_suggestions = results.get('ai_suggestions', {})
        
        print("=== FULL AI SUGGESTIONS STRUCTURE ===")
        print(json.dumps(ai_suggestions, indent=2))
        
        if 'categories' in ai_suggestions:
            print("\n=== SAMPLE TOOL FROM EACH CATEGORY ===")
            for category, tools in ai_suggestions['categories'].items():
                if tools:
                    print(f"\n{category} - Sample Tool:")
                    print(json.dumps(tools[0], indent=2))
    
    db.close()

if __name__ == "__main__":
    debug_data_structure()