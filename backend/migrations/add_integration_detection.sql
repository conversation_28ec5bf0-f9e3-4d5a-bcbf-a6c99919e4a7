-- Migration: Add Integration Detection and Indexing Support
-- This migration adds new columns to repo_analyses table and creates detected_integrations table

-- Add new columns to repo_analyses table
ALTER TABLE repo_analyses 
ADD COLUMN indexing_status VARCHAR DEFAULT 'not_started',
ADD COLUMN indexing_progress INTEGER DEFAULT 0,
ADD COLUMN vector_db_id VARCHAR,
ADD COLUMN integration_analysis JSON,
ADD COLUMN last_indexed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN code_hash VARCHAR;

-- <PERSON><PERSON> detected_integrations table
CREATE TABLE detected_integrations (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER NOT NULL REFERENCES repo_analyses(id) ON DELETE CASCADE,
    integration_type VARCHAR NOT NULL,
    service_name VARCHAR NOT NULL,
    detection_method VARCHAR NOT NULL,
    confidence FLOAT NOT NULL DEFAULT 0.0,
    file_locations JSON,
    package_names JSON,
    code_patterns JSON,
    env_variables JSO<PERSON>,
    migration_complexity VARCHAR NOT NULL,
    description TEXT,
    mcp_alternatives JSON,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX idx_detected_integrations_analysis_id ON detected_integrations(analysis_id);
CREATE INDEX idx_detected_integrations_type ON detected_integrations(integration_type);
CREATE INDEX idx_detected_integrations_service ON detected_integrations(service_name);
CREATE INDEX idx_repo_analyses_indexing_status ON repo_analyses(indexing_status);
CREATE INDEX idx_repo_analyses_vector_db_id ON repo_analyses(vector_db_id);

-- Add comments for documentation
COMMENT ON COLUMN repo_analyses.indexing_status IS 'Status of code indexing: not_started, indexing, completed, failed';
COMMENT ON COLUMN repo_analyses.indexing_progress IS 'Progress of indexing from 0 to 100';
COMMENT ON COLUMN repo_analyses.vector_db_id IS 'ID of the document in vector database';
COMMENT ON COLUMN repo_analyses.integration_analysis IS 'JSON containing detected integrations summary';
COMMENT ON COLUMN repo_analyses.last_indexed_at IS 'Timestamp when code was last indexed';
COMMENT ON COLUMN repo_analyses.code_hash IS 'Hash of repository code to detect changes';

COMMENT ON TABLE detected_integrations IS 'Third-party integrations detected in repositories';
COMMENT ON COLUMN detected_integrations.integration_type IS 'Type of integration: payment, email, sms, cloud_storage, database, analytics';
COMMENT ON COLUMN detected_integrations.service_name IS 'Name of the service: stripe, sendgrid, twilio, aws, etc.';
COMMENT ON COLUMN detected_integrations.detection_method IS 'How it was detected: package_dependency, code_pattern, environment_variable';
COMMENT ON COLUMN detected_integrations.confidence IS 'Confidence score from 0.0 to 1.0';
COMMENT ON COLUMN detected_integrations.migration_complexity IS 'Migration complexity: low, medium, high';
COMMENT ON COLUMN detected_integrations.mcp_alternatives IS 'JSON array of available MCP alternatives';
