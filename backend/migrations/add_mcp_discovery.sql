-- Migration: Add MCP Discovery Tables
-- Description: Add tables for storing discovered MCP servers and categories
-- Date: 2024-12-19

-- Create MCP categories table
CREATE TABLE mcp_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR,
    color VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create MCP servers table
CREATE TABLE mcp_servers (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL,
    url VARCHAR NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR NOT NULL,
    github_url VARCHAR,
    npm_url VARCHAR,
    documentation_url VARCHAR,
    stars INTEGER,
    language VARCHAR,
    confidence_score FLOAT NOT NULL DEFAULT 0.0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_mcp_servers_name ON mcp_servers(name);
CREATE INDEX idx_mcp_servers_category ON mcp_servers(category);
CREATE INDEX idx_mcp_servers_language ON mcp_servers(language);
CREATE INDEX idx_mcp_servers_confidence ON mcp_servers(confidence_score);
CREATE INDEX idx_mcp_servers_stars ON mcp_servers(stars);
CREATE INDEX idx_mcp_servers_active ON mcp_servers(is_active);

-- Insert default categories based on MCP Market Map
INSERT INTO mcp_categories (name, description, icon, color) VALUES
('database', 'Database and storage integrations', '🗄️', '#4F46E5'),
('ai_chat', 'AI and chat applications', '🤖', '#7C3AED'),
('development', 'Development tools and coding', '💻', '#059669'),
('productivity', 'Productivity and workflow tools', '⚡', '#DC2626'),
('cloud', 'Cloud services and storage', '☁️', '#2563EB'),
('monitoring', 'Monitoring and observability', '📊', '#EA580C'),
('search', 'Search and scraping tools', '🔍', '#0891B2'),
('payment', 'Payment processing', '💳', '#16A34A'),
('communication', 'Email, SMS, and messaging', '📧', '#C2410C'),
('design', 'Design and creative tools', '🎨', '#DB2777'),
('ticketing', 'Issue tracking and support', '🎫', '#7C2D12'),
('general', 'General purpose tools', '🔧', '#6B7280');

-- Insert some initial MCP servers from the market map
INSERT INTO mcp_servers (name, url, description, category, github_url, language, confidence_score) VALUES
('sqlite-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite', 'SQLite database MCP server', 'database', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.95),
('postgres-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/postgres', 'PostgreSQL database MCP server', 'database', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.95),
('github-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/github', 'GitHub integration MCP server', 'development', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.95),
('slack-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/slack', 'Slack integration MCP server', 'communication', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.95),
('notion-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/notion', 'Notion integration MCP server', 'productivity', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.95),
('filesystem-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem', 'Local filesystem MCP server', 'general', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.95),
('tavily-mcp', 'https://github.com/tavily-ai/mcp-server', 'Tavily AI search MCP server', 'search', 'https://github.com/tavily-ai/mcp-server', 'python', 0.90),
('stripe-mcp', 'https://github.com/modelcontextprotocol/servers/tree/main/src/stripe', 'Stripe payment MCP server', 'payment', 'https://github.com/modelcontextprotocol/servers', 'javascript', 0.90),
('resend-mcp', 'https://github.com/resend/mcp-server', 'Resend email MCP server', 'communication', 'https://github.com/resend/mcp-server', 'javascript', 0.85),
('sentry-mcp', 'https://github.com/getsentry/mcp-server', 'Sentry error tracking MCP server', 'monitoring', 'https://github.com/getsentry/mcp-server', 'python', 0.85),
('linear-mcp', 'https://github.com/linear/mcp-server', 'Linear issue tracking MCP server', 'ticketing', 'https://github.com/linear/mcp-server', 'javascript', 0.90),
('figma-mcp', 'https://github.com/figma/mcp-server', 'Figma design MCP server', 'design', 'https://github.com/figma/mcp-server', 'javascript', 0.85),
('firecrawl-mcp', 'https://github.com/firecrawl/mcp-server', 'Firecrawl web scraping MCP server', 'search', 'https://github.com/firecrawl/mcp-server', 'python', 0.80),
('browserbase-mcp', 'https://github.com/browserbase/mcp-server', 'Browserbase automation MCP server', 'search', 'https://github.com/browserbase/mcp-server', 'javascript', 0.80);

-- Add comments
COMMENT ON TABLE mcp_servers IS 'Discovered MCP servers from web search and manual curation';
COMMENT ON TABLE mcp_categories IS 'Categories for organizing MCP servers';
COMMENT ON COLUMN mcp_servers.confidence_score IS 'Confidence score from 0.0 to 1.0 for server quality/reliability';
COMMENT ON COLUMN mcp_servers.category IS 'Category of the MCP server (references mcp_categories.name)';
COMMENT ON COLUMN mcp_servers.stars IS 'GitHub stars count if available';
COMMENT ON COLUMN mcp_servers.language IS 'Primary programming language of the server';
