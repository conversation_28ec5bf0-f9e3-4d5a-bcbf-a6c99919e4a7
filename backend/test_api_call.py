#!/usr/bin/env python3
"""
Test script to simulate API call to MCP suggestions endpoint
"""
import requests
import json
import os
from app.database import get_db
from app.models import User, RepoAnalysis
from app.utils.auth import create_access_token
from sqlalchemy.orm import Session

def test_mcp_suggestions_api():
    # Test without authentication first
    print("=== Testing API without authentication ===")
    response = requests.get("http://localhost:8000/api/v1/mcp-generate/22/suggestions")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Now test with mock authentication by examining the database directly
    print("\n=== Examining database data ===")
    
    # Get database session
    db = next(get_db())
    
    # Get analysis data
    analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == 22).first()
    if analysis:
        print(f"Analysis ID: {analysis.id}")
        print(f"Status: {analysis.status}")
        print(f"User ID: {analysis.user_id}")
        
        # Check if ai_suggestions exist
        results = analysis.analysis_results or {}
        has_ai_suggestions = 'ai_suggestions' in results
        print(f"Has AI suggestions: {has_ai_suggestions}")
        
        if has_ai_suggestions:
            ai_suggestions = results.get('ai_suggestions', {})
            print(f"AI suggestions keys: {list(ai_suggestions.keys())}")
            
            # Print structure to understand format
            if 'categories' in ai_suggestions:
                categories = ai_suggestions['categories']
                print(f"Categories: {list(categories.keys())}")
                for cat, tools in categories.items():
                    print(f"  {cat}: {len(tools)} tools")
        
        # Print what the API should return
        expected_response = {
            "analysis_id": analysis.id,
            "suggestions_available": True,
            "mcp_suggestions": results.get("ai_suggestions", {}),
            "business_analysis": results.get("business_analysis", {}),
            "confidence_score": results.get("confidence_score", 0.0),
            "implementation_complexity": results.get("implementation_complexity", {})
        }
        
        print(f"\n=== Expected API Response Structure ===")
        print(f"suggestions_available: {expected_response['suggestions_available']}")
        print(f"mcp_suggestions keys: {list(expected_response['mcp_suggestions'].keys())}")
        print(f"confidence_score: {expected_response['confidence_score']}")
        
    else:
        print("Analysis not found")
    
    db.close()

if __name__ == "__main__":
    test_mcp_suggestions_api()