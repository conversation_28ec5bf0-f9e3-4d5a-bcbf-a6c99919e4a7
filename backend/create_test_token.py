#!/usr/bin/env python3
"""
Create test JWT token for API testing
"""
from jose import jwt
from datetime import datetime, timedelta

# Settings from .env file
JWT_SECRET = "4f8b2c9e1a7d3f6b8e2c5a9f1d4e7b3c9f2e5a8d1c4f7b9e2a5d8c1f4e7b3c6a9"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=JWT_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return encoded_jwt

if __name__ == "__main__":
    # Create token for user ID 1
    token = create_access_token(data={"sub": "1"})
    print(f"JWT Token for user ID 1: {token}")