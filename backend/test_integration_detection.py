#!/usr/bin/env python3
"""
Test script for integration detection functionality
"""

import asyncio
import json
from app.services.integration_detector import IntegrationDetector

async def test_integration_detection():
    """Test the integration detection with sample repository content"""
    
    # Sample repository content that would come from analysis
    sample_repo_content = {
        "dependencies": [
            {"name": "stripe", "version": "5.4.0", "file_path": "requirements.txt"},
            {"name": "sendgrid", "version": "6.9.7", "file_path": "requirements.txt"},
            {"name": "twilio", "version": "7.16.0", "file_path": "requirements.txt"},
            {"name": "boto3", "version": "1.26.0", "file_path": "requirements.txt"},
            {"name": "redis", "version": "4.5.0", "file_path": "requirements.txt"}
        ],
        "code_samples": {
            "payment.py": """
import stripe
from stripe import PaymentIntent

def create_payment(amount, currency='usd'):
    stripe.api_key = os.getenv('STRIPE_SECRET_KEY')
    
    payment_intent = stripe.PaymentIntent.create(
        amount=amount,
        currency=currency,
        automatic_payment_methods={'enabled': True}
    )
    return payment_intent
""",
            "email_service.py": """
import sendgrid
from sendgrid.helpers.mail import Mail

def send_email(to_email, subject, content):
    sg = sendgrid.SendGridAPIClient(api_key=os.environ.get('SENDGRID_API_KEY'))
    
    message = Mail(
        from_email='<EMAIL>',
        to_emails=to_email,
        subject=subject,
        html_content=content
    )
    
    response = sg.send(message)
    return response
""",
            "sms_service.py": """
from twilio.rest import Client

def send_sms(to_number, message):
    account_sid = os.environ['TWILIO_ACCOUNT_SID']
    auth_token = os.environ['TWILIO_AUTH_TOKEN']
    
    client = Client(account_sid, auth_token)
    
    message = client.messages.create(
        body=message,
        from_='+**********',
        to=to_number
    )
    
    return message.sid
""",
            ".env.example": """
# Payment
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...

# Email
SENDGRID_API_KEY=SG.xxx

# SMS
TWILIO_ACCOUNT_SID=ACxxx
TWILIO_AUTH_TOKEN=xxx

# Cloud Storage
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx

# Database
REDIS_URL=redis://localhost:6379
""",
            "config.py": """
import os

class Config:
    STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY')
    SENDGRID_API_KEY = os.environ.get('SENDGRID_API_KEY')
    TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    REDIS_URL = os.environ.get('REDIS_URL')
"""
        }
    }
    
    print("🔍 Testing Integration Detection...")
    print("=" * 50)
    
    # Initialize detector
    detector = IntegrationDetector()
    
    # Detect integrations
    detected_integrations = await detector.detect_integrations(sample_repo_content)
    
    print(f"✅ Detected {len(detected_integrations)} integrations:")
    print()
    
    for integration in detected_integrations:
        print(f"🔧 {integration.service_name.upper()} ({integration.integration_type})")
        print(f"   Confidence: {integration.confidence:.2f}")
        print(f"   Detection: {integration.detection_method}")
        print(f"   Complexity: {integration.migration_complexity}")
        print(f"   Files: {', '.join(integration.file_locations)}")
        if integration.package_names:
            print(f"   Packages: {', '.join(integration.package_names)}")
        if integration.env_variables:
            print(f"   Env Vars: {', '.join(integration.env_variables)}")
        print(f"   Description: {integration.description}")
        print()
    
    print("=" * 50)
    print("✅ Integration detection test completed!")
    
    # Test JSON serialization (for storing in database)
    serializable_data = []
    for integration in detected_integrations:
        serializable_data.append({
            "integration_type": integration.integration_type,
            "service_name": integration.service_name,
            "detection_method": integration.detection_method,
            "confidence": integration.confidence,
            "file_locations": integration.file_locations,
            "package_names": integration.package_names,
            "code_patterns": integration.code_patterns,
            "env_variables": integration.env_variables,
            "migration_complexity": integration.migration_complexity,
            "description": integration.description
        })
    
    print("\n📄 JSON Serialization Test:")
    print(json.dumps(serializable_data, indent=2))

if __name__ == "__main__":
    asyncio.run(test_integration_detection())
