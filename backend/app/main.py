from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from .config import settings
from .api import auth, analysis, mcp_generation

app = FastAPI(
    title=settings.project_name,
    version=settings.project_version,
    description="""
    SuperMCP API - GitHub Repository Analysis for MCP Server Potential
    
    ## Features
    
    * **Repository Analysis**: Comprehensive analysis of GitHub repositories for MCP server potential
    * **Multi-language Support**: Support for Python, JavaScript/TypeScript, Go, Java, Rust, PHP, Ruby, C#
    * **Background Processing**: Celery-based background analysis with real-time progress tracking
    * **MCP Server Generation**: Generate complete MCP server implementations from analysis results
    * **OAuth Integration**: Secure GitHub OAuth authentication
    
    ## Authentication
    
    All endpoints require authentication using GitHub OAuth. Include the JWT token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Analysis Workflow
    
    1. **Authenticate** with GitHub OAuth
    2. **Start Analysis** by providing a GitHub repository URL
    3. **Monitor Progress** using the status endpoint with task tracking
    4. **Review Results** including MCP feasibility score, dependencies, and recommendations
    5. **Generate MCP Server** from the analysis results
    
    ## MCP Feasibility Scoring
    
    Repositories are scored 0-100 based on:
    - Code structure complexity (30%)
    - API endpoints presence (20 points)
    - CLI interface indicators (15 points) 
    - Relevant dependencies (15 points)
    - Repository characteristics (10 points)
    - Multi-language support (5 points)
    - Database integration (10 points)
    """,
    openapi_url=f"{settings.api_v1_str}/openapi.json",
    docs_url=f"{settings.api_v1_str}/docs",
    redoc_url=f"{settings.api_v1_str}/redoc",
    contact={
        "name": "SuperMCP API Support",
        "url": "https://github.com/your-org/supermcp",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    openapi_tags=[
        {
            "name": "auth",
            "description": "Authentication endpoints using GitHub OAuth"
        },
        {
            "name": "analysis", 
            "description": "Repository analysis endpoints for MCP potential assessment"
        },
        {
            "name": "mcp-generation",
            "description": "MCP server code generation endpoints"
        }
    ]
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix=f"{settings.api_v1_str}/auth", tags=["auth"])
app.include_router(analysis.router, prefix=f"{settings.api_v1_str}/analysis", tags=["analysis"])
app.include_router(mcp_generation.router, prefix=f"{settings.api_v1_str}/mcp-generate", tags=["mcp-generation"])


@app.get("/")
async def root():
    return {"message": "SuperMCP API", "version": settings.project_version}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}