from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Query
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from celery.result import AsyncResult
from ..database import get_db
from ..models import User, RepoAnalysis, Dependency
from ..schemas.analysis import AnalysisCreate, AnalysisResponse, DependencyResponse, AnalysisStats
from ..services.analysis_service import AnalysisService
from ..utils.auth import get_current_user
from ..tasks.real_analysis import analyze_repository_real
from ..tasks.enhanced_analysis import analyze_repository_enhanced
from ..tasks.celery_app import celery_app
from ..services.indexing_service import IndexingService

router = APIRouter()
security = HTTPBearer()


@router.post("/", response_model=AnalysisResponse)
async def create_analysis(
    analysis_data: AnalysisCreate,
    force_reanalysis: bool = Query(False, description="Force reanalysis of existing repository"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new repository analysis"""
    
    # Check if repository was already analyzed recently
    if not force_reanalysis:
        existing_analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.user_id == current_user.id,
            RepoAnalysis.repo_url == str(analysis_data.repo_url),
            RepoAnalysis.status == "completed",
            RepoAnalysis.created_at >= datetime.utcnow() - timedelta(hours=24)  # Within last 24 hours
        ).first()
        
        if existing_analysis:
            return existing_analysis
    
    # Create analysis record
    analysis = RepoAnalysis(
        user_id=current_user.id,
        repo_url=str(analysis_data.repo_url),
        repo_name=analysis_data.repo_name,
        repo_owner=analysis_data.repo_owner,
        status="pending"
    )
    
    db.add(analysis)
    db.commit()
    db.refresh(analysis)
    
    # Start background analysis task (use enhanced analysis if any AI API key is available)
    from ..config import settings
    if settings.anthropic_api_key or settings.openai_api_key:
        task = analyze_repository_enhanced.delay(analysis.id, current_user.github_token)
    else:
        task = analyze_repository_real.delay(analysis.id, current_user.github_token)
    
    # Store task ID for progress tracking
    analysis.analysis_results = {"task_id": task.id}
    db.commit()
    
    return analysis


@router.get("/", response_model=List[AnalysisResponse])
async def get_user_analyses(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all analyses for the current user"""
    
    analyses = db.query(RepoAnalysis).filter(
        RepoAnalysis.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    return analyses


@router.get("/history", response_model=List[AnalysisResponse])
async def get_analysis_history(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    repo_name: Optional[str] = Query(None, description="Filter by repository name"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's analysis history with filtering and pagination"""
    
    query = db.query(RepoAnalysis).filter(RepoAnalysis.user_id == current_user.id)
    
    if status_filter:
        query = query.filter(RepoAnalysis.status == status_filter)
    
    if repo_name:
        query = query.filter(RepoAnalysis.repo_name.ilike(f"%{repo_name}%"))
    
    analyses = query.order_by(desc(RepoAnalysis.created_at)).offset(skip).limit(limit).all()
    
    return analyses


@router.get("/stats", response_model=AnalysisStats)
async def get_analysis_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's analysis statistics"""
    
    # Get counts by status
    stats = db.query(
        RepoAnalysis.status,
        func.count(RepoAnalysis.id).label('count')
    ).filter(
        RepoAnalysis.user_id == current_user.id
    ).group_by(RepoAnalysis.status).all()
    
    # Convert to dict
    status_counts = {stat.status: stat.count for stat in stats}
    
    # Calculate average MCP score for completed analyses
    avg_score = db.query(
        func.avg(RepoAnalysis.mcp_feasibility_score)
    ).filter(
        RepoAnalysis.user_id == current_user.id,
        RepoAnalysis.status == "completed",
        RepoAnalysis.mcp_feasibility_score.isnot(None)
    ).scalar()
    
    print(f"Status Counts: {status_counts}")
    print(f"Average Score: {avg_score}")
    return AnalysisStats(
        total_analyses=sum(status_counts.values()),
        completed_analyses=status_counts.get("completed", 0),
        failed_analyses=status_counts.get("failed", 0),
        pending_analyses=status_counts.get("pending", 0) + status_counts.get("analyzing", 0),
        average_mcp_score=float(avg_score) if avg_score else None
    )


@router.get("/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get analysis results by ID"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    return analysis


@router.delete("/{analysis_id}")
async def delete_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete an analysis"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    db.delete(analysis)
    db.commit()
    
    return {"message": "Analysis deleted successfully"}


@router.get("/{analysis_id}/status")
async def get_analysis_status(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get real-time analysis status and progress"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    response = {
        "analysis_id": analysis_id,
        "status": analysis.status,
        "created_at": analysis.created_at,
        "updated_at": analysis.updated_at,
        "completed_at": analysis.completed_at,
        "error_message": analysis.error_message
    }
    
    # Get task progress if available
    task_id = None
    if analysis.analysis_results and isinstance(analysis.analysis_results, dict):
        task_id = analysis.analysis_results.get("task_id")
    
    if task_id:
        try:
            task_result = AsyncResult(task_id, app=celery_app)
            response.update({
                "task_id": task_id,
                "task_status": task_result.status,
                "task_info": task_result.info
            })
        except Exception as e:
            response["task_error"] = str(e)
    
    return response


@router.get("/{analysis_id}/dependencies", response_model=List[DependencyResponse])
async def get_analysis_dependencies(
    analysis_id: int,
    language: Optional[str] = Query(None, description="Filter by programming language"),
    dependency_type: Optional[str] = Query(None, description="Filter by dependency type"),
    min_mcp_potential: Optional[float] = Query(None, description="Minimum MCP potential score"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dependencies found in the analysis"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Build query with filters
    query = db.query(Dependency).filter(Dependency.analysis_id == analysis_id)
    
    if language:
        query = query.filter(Dependency.language == language)
    
    if dependency_type:
        query = query.filter(Dependency.dependency_type == dependency_type)
    
    if min_mcp_potential is not None:
        query = query.filter(Dependency.mcp_potential >= min_mcp_potential)
    
    dependencies = query.order_by(desc(Dependency.mcp_potential)).all()
    
    return dependencies


@router.post("/{analysis_id}/retry")
async def retry_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Retry a failed analysis"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status not in ["failed", "completed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only retry failed or completed analyses"
        )
    
    # Reset analysis status
    analysis.status = "pending"
    analysis.error_message = None
    analysis.completed_at = None
    
    # Clear previous results but keep dependencies for reference
    analysis.analysis_results = None
    analysis.mcp_feasibility_score = None
    
    db.commit()
    
    # Start new analysis task (use enhanced analysis if any AI API key is available)
    if settings.anthropic_api_key or settings.openai_api_key:
        task = analyze_repository_enhanced.delay(analysis.id, current_user.github_token)
    else:
        task = analyze_repository_real.delay(analysis.id, current_user.github_token)
    
    # Store new task ID
    analysis.analysis_results = {"task_id": task.id}
    db.commit()
    
    return {"message": "Analysis retry started", "task_id": task.id}


@router.get("/{analysis_id}/export")
async def export_analysis(
    analysis_id: int,
    format: str = Query("json", regex="^(json|csv)$", description="Export format"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export analysis results in different formats"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only export completed analyses"
        )
    
    # Get dependencies
    dependencies = db.query(Dependency).filter(Dependency.analysis_id == analysis_id).all()
    
    export_data = {
        "analysis": {
            "id": analysis.id,
            "repo_url": analysis.repo_url,
            "repo_name": analysis.repo_name,
            "repo_owner": analysis.repo_owner,
            "mcp_feasibility_score": analysis.mcp_feasibility_score,
            "status": analysis.status,
            "created_at": analysis.created_at.isoformat(),
            "completed_at": analysis.completed_at.isoformat() if analysis.completed_at else None,
            "analysis_results": analysis.analysis_results
        },
        "dependencies": [
            {
                "name": dep.name,
                "version": dep.version,
                "language": dep.language,
                "dependency_type": dep.dependency_type,
                "file_path": dep.file_path,
                "mcp_potential": dep.mcp_potential,
                "existing_mcp_servers": dep.existing_mcp_servers
            }
            for dep in dependencies
        ]
    }
    
    if format == "json":
        return export_data
    elif format == "csv":
        # For CSV, flatten the data structure
        import io
        import csv
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "Analysis ID", "Repository", "MCP Score", "Dependency Name", 
            "Version", "Language", "Type", "File Path", "MCP Potential"
        ])
        
        # Write data rows
        for dep in dependencies:
            writer.writerow([
                analysis.id,
                f"{analysis.repo_owner}/{analysis.repo_name}",
                analysis.mcp_feasibility_score,
                dep.name,
                dep.version,
                dep.language,
                dep.dependency_type,
                dep.file_path,
                dep.mcp_potential
            ])
        
        return {"csv_data": output.getvalue()}

    return export_data


@router.get("/{analysis_id}/indexing-status")
async def get_indexing_status(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current indexing status for an analysis"""

    # Verify analysis belongs to user
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    indexing_service = IndexingService()
    status_info = await indexing_service.get_indexing_status(analysis_id)

    return status_info


@router.post("/{analysis_id}/reindex")
async def reindex_repository(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Trigger re-indexing of repository code"""

    # Verify analysis belongs to user
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if not analysis.analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before indexing"
        )

    # Get repository content from analysis results
    repo_content = analysis.analysis_results

    indexing_service = IndexingService()
    result = await indexing_service.trigger_reindex(analysis_id, repo_content)

    return {
        "message": "Re-indexing started",
        "result": result
    }


@router.get("/{analysis_id}/integrations")
async def get_detected_integrations(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detected third-party integrations for an analysis"""

    # Verify analysis belongs to user
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    # Get detected integrations from analysis results
    detected_integrations = []
    mcp_alternatives = {}

    if analysis.analysis_results:
        detected_integrations = analysis.analysis_results.get("detected_integrations", [])
        mcp_alternatives = analysis.analysis_results.get("mcp_alternatives", {})

    return {
        "detected_integrations": detected_integrations,
        "mcp_alternatives": mcp_alternatives,
        "total_integrations": len(detected_integrations),
        "has_alternatives": len(mcp_alternatives) > 0
    }