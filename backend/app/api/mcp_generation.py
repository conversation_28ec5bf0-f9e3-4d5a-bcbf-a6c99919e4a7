"""
MCP Generation API endpoints
Handles MCP tool suggestions and server code generation
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging
import os

from ..database import get_db
from ..models import User, RepoAnalysis
from ..utils.auth import get_current_user
from ..services.mcp_code_generator import MCPCodeGeneratorService
from ..tasks.enhanced_analysis import generate_mcp_suggestions_only

logger = logging.getLogger(__name__)
router = APIRouter()


def transform_suggestions_for_frontend(ai_suggestions: Dict[str, Any]) -> Dict[str, Any]:
    """Transform enhanced AI suggestions to match frontend interface expectations"""

    try:
        # Handle enhanced analysis structure - check if it's already in the new format
        if "categories" in ai_suggestions and "repository_analysis" in ai_suggestions:
            # New enhanced format - return as-is with minimal transformation
            categories = ai_suggestions.get("categories", {})
        
        # Ensure all tools have required fields
        for category_name, tools in categories.items():
            if not isinstance(tools, list):
                continue
            for tool in tools:
                if not isinstance(tool, dict):
                    continue
                # Ensure complexity_level is properly set
                if "complexity_level" not in tool and "estimated_effort_hours" in tool:
                    hours = tool["estimated_effort_hours"]
                    if hours <= 8:
                        tool["complexity_level"] = "low"
                    elif hours <= 16:
                        tool["complexity_level"] = "medium"
                    else:
                        tool["complexity_level"] = "high"
                elif "complexity_level" not in tool:
                    tool["complexity_level"] = "medium"

                # Ensure required fields exist
                tool.setdefault("tool_name", tool.get("name", "Unknown Tool"))
                tool.setdefault("description", "No description available")
                tool.setdefault("business_value", "No description available")
                tool.setdefault("input_schema", {})
                tool.setdefault("output_schema", {})
                tool.setdefault("implementation_hints", "")
                tool.setdefault("use_cases", [])
                tool.setdefault("dependencies", [])
                tool.setdefault("error_scenarios", [])
        
            return {
                "categories": categories,
                "prioritized_recommendations": ai_suggestions.get("prioritized_recommendations", []),
                "implementation_roadmap": ai_suggestions.get("implementation_roadmap", {}),
                "total_tools_suggested": ai_suggestions.get("total_tools_suggested", 0),
                "repository_analysis": ai_suggestions.get("repository_analysis", {}),
                "tool_generation_strategy": ai_suggestions.get("tool_generation_strategy", {})
            }

        # Legacy format transformation (for older analyses)
        def get_complexity_level(implementation_effort: str) -> str:
            """Convert implementation effort to complexity level"""
            effort_lower = implementation_effort.lower() if implementation_effort else "medium"
            if "low" in effort_lower or "easy" in effort_lower or "simple" in effort_lower:
                return "low"
            elif "high" in effort_lower or "hard" in effort_lower or "complex" in effort_lower:
                return "high"
            else:
                return "medium"

        # Transform categories with proper field mapping for legacy format
        transformed_categories = {}
        categories = ai_suggestions.get("categories", {})

        for category_name, tools in categories.items():
            transformed_tools = []

            for tool in tools:
                # Transform each tool to match frontend interface
                transformed_tool = {
                    "tool_name": tool.get("tool_name", tool.get("name", "Unknown Tool")),
                    "description": tool.get("description", tool.get("business_value", "No description available")),
                    "business_value": tool.get("business_value", "No description available"),
                    "complexity_level": tool.get("complexity_level", get_complexity_level(tool.get("implementation_effort", "medium"))),
                    "input_schema": tool.get("input_schema", {}),
                    "output_schema": tool.get("output_schema", {}),
                    "implementation_hints": tool.get("implementation_hints", ""),
                    "use_cases": tool.get("use_cases", []),
                    "dependencies": tool.get("dependencies", []),
                    "error_scenarios": tool.get("error_scenarios", [])
                }
                transformed_tools.append(transformed_tool)

            transformed_categories[category_name] = transformed_tools

        # Return transformed suggestions
        return {
            "categories": transformed_categories,
            "prioritized_recommendations": ai_suggestions.get("prioritized_recommendations", []),
            "implementation_roadmap": ai_suggestions.get("implementation_roadmap", {}),
            "total_tools_suggested": ai_suggestions.get("total_tools_suggested", 0)
        }

    except Exception as e:
        logger.error(f"Error transforming suggestions for frontend: {str(e)}")
        # Return empty structure on error
        return {
            "categories": {},
            "prioritized_recommendations": [],
            "implementation_roadmap": {},
            "total_tools_suggested": 0
        }


# Request/Response Models
class GenerateMCPServerRequest(BaseModel):
    analysis_id: int
    selected_tools: List[str]  # List of tool names
    target_language: str = "typescript"
    hosting_architecture: str = "http-sse"
    customization_options: Optional[Dict[str, Any]] = None


# Endpoints
@router.get("/{analysis_id}/suggestions")
async def get_mcp_suggestions(
    analysis_id: int,
    regenerate: bool = Query(False, description="Force regenerate suggestions"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get MCP tool suggestions for an analysis"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before generating suggestions"
        )
    
    # Check if suggestions already exist and user doesn't want to regenerate
    analysis_results = analysis.analysis_results or {}
    logger.info(f"Analysis results keys: {list(analysis_results.keys())}")
    
    if not regenerate and "ai_suggestions" in analysis_results:
        # Transform suggestions to frontend format
        ai_suggestions = analysis_results.get("ai_suggestions", {})
        logger.info(f"AI suggestions structure: {ai_suggestions.keys() if ai_suggestions else 'Empty'}")
        logger.info(f"AI suggestions sample: {str(ai_suggestions)[:500]}...")
        
        try:
            transformed_suggestions = transform_suggestions_for_frontend(ai_suggestions)
            logger.info(f"Transformed suggestions successfully")
            
            # Return existing suggestions with enhanced structure
            return {
                "analysis_id": analysis_id,
                "suggestions_available": True,
                "mcp_suggestions": transformed_suggestions,
                "business_analysis": analysis_results.get("business_analysis", analysis_results.get("comprehensive_analysis", {})),
                "confidence_score": analysis_results.get("confidence_score", 0.0),
                "implementation_complexity": analysis_results.get("implementation_complexity", {})
            }
        except Exception as e:
            logger.error(f"Error transforming suggestions: {str(e)}")
            logger.exception("Full error details:")
            # Fall through to regeneration logic
    
    # Generate new suggestions
    try:
        # Check if user has GitHub token
        github_token = getattr(current_user, 'github_token', None)
        if not github_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="GitHub token required for suggestion generation"
            )
            
        task = generate_mcp_suggestions_only.delay(analysis_id, github_token)
        
        return {
            "analysis_id": analysis_id,
            "suggestions_available": False,
            "task_id": task.id,
            "status": "generating",
            "message": "MCP suggestions are being generated. Please check back in a moment."
        }
        
    except Exception as e:
        logger.error(f"Failed to start MCP suggestions generation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate MCP suggestions: {str(e)}"
        )


@router.post("/{analysis_id}/generate-server")
async def generate_mcp_server(
    analysis_id: int,
    request: GenerateMCPServerRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP server code based on selected tools"""
    
    logger.info(f"MCP Generation Request - Language: {request.target_language}, Architecture: {request.hosting_architecture}, Tools: {len(request.selected_tools)}")
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before generating MCP server"
        )
    
    analysis_results = analysis.analysis_results or {}
    
    # Check if MCP suggestions are available
    if "ai_suggestions" not in analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="MCP suggestions not available. Please generate suggestions first."
        )
    
    # Get MCP suggestions and find selected tools
    ai_suggestions = analysis_results.get("ai_suggestions", {})
    
    # Transform suggestions to frontend format to ensure consistent tool_name field
    mcp_suggestions = transform_suggestions_for_frontend(ai_suggestions)
    all_tools = {}
    
    # Collect all tools from all categories
    for tools in mcp_suggestions.get("categories", {}).values():
        for tool in tools:
            all_tools[tool["tool_name"]] = tool
    
    # Filter selected tools
    selected_tools = []
    for tool_name in request.selected_tools:
        if tool_name in all_tools:
            selected_tools.append(all_tools[tool_name])
        else:
            logger.warning(f"Tool '{tool_name}' not found in available suggestions")
    
    if not selected_tools:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid tools selected for generation"
        )
    
    # Prepare repository context
    repo_context = {
        "name": analysis.repo_name,
        "owner": analysis.repo_owner,
        "url": analysis.repo_url,
        "language": analysis_results.get("repository_info", {}).get("language"),
        "description": analysis_results.get("repository_info", {}).get("description")
    }
    
    # Generate MCP server code
    try:
        generator = MCPCodeGeneratorService()
        
        generation_result = await generator.generate_mcp_server(
            selected_tools=selected_tools,
            target_language=request.target_language,
            hosting_architecture=request.hosting_architecture,
            repo_context=repo_context,
            analysis_data=analysis_results,
            customization_options=request.customization_options
        )
        
        return {
            "success": True,
            "analysis_id": analysis_id,
            "generation_result": generation_result,
            "download_ready": True
        }
        
    except Exception as e:
        logger.error(f"MCP server generation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate MCP server: {str(e)}"
        )


@router.get("/{analysis_id}/download-server")
async def download_mcp_server(
    analysis_id: int,
    zip_file_path: str = Query(..., description="Path to generated ZIP file"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Download generated MCP server ZIP file"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Verify ZIP file exists
    if not os.path.exists(zip_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generated MCP server file not found"
        )
    
    # Return file for download
    filename = f"mcp-server-{analysis.repo_name}-{analysis_id}.zip"
    
    return FileResponse(
        zip_file_path,
        media_type="application/zip",
        filename=filename,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/languages/supported")
async def get_supported_languages():
    """Get list of supported programming languages for MCP server generation"""
    
    return {
        "supported_languages": [
            {
                "code": "python",
                "name": "Python",
                "description": "Python MCP server using @modelcontextprotocol/server-python",
                "features": ["Type hints", "Pydantic validation", "Async support", "Rich ecosystem"],
                "recommended": True
            },
            {
                "code": "typescript", 
                "name": "TypeScript",
                "description": "TypeScript MCP server using @modelcontextprotocol/sdk",
                "features": ["Strong typing", "Modern JS features", "Node.js ecosystem", "IDE support"],
                "recommended": True
            },
            {
                "code": "javascript",
                "name": "JavaScript",
                "description": "JavaScript MCP server using @modelcontextprotocol/sdk", 
                "features": ["Quick development", "Node.js ecosystem", "Wide compatibility"],
                "recommended": False
            }
        ],
        "default_language": "python"
    }