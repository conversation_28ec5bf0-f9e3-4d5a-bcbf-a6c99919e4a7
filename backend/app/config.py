from pydantic_settings import BaseSettings
from typing import List, Optional, Any
import os
import json
from pydantic import field_validator


class Settings(BaseSettings):
    # Database - Use PostgreSQL for all environments
    database_url: str = "**************************************/supermcp"
    
    # GitHub OAuth
    github_client_id: str
    github_client_secret: str
    github_redirect_url: str = "http://localhost:3000/auth/callback"
    
    # JWT
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    
    # External APIs
    tavily_api_key: Optional[str] = None
    context7_mcp_url: Optional[str] = None
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None

    # Vector Database (Redis)
    redis_vector_url: str = "redis://localhost:6379/2"
    
    # API Configuration
    api_v1_str: str = "/api/v1"
    project_name: str = "SuperMCP"
    project_version: str = "1.0.0"
    
    # Security
    secret_key: str
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]

    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v: Any) -> Any:
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("CORS_ORIGINS must be a valid JSON array string")
        return v
    
    # Celery
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    
    # Development
    debug: bool = False
    environment: str = "development"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


settings = Settings()