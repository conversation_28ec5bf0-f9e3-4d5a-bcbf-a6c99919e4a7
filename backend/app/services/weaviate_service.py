"""
Weaviate Vector Database Service

Handles code indexing and retrieval using Weaviate vector database.
Provides semantic search capabilities for better code understanding.
"""

import asyncio
import json
import logging
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime
import weaviate
from ..config import settings

logger = logging.getLogger(__name__)


class WeaviateService:
    """Service for managing code indexing in Weaviate vector database"""
    
    def __init__(self):
        self.client = None
        self.collection_name = "CodeChunks"
        self.max_chunk_size = 1000
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Weaviate client doesn't need explicit closing in this version
        pass
    
    async def connect(self):
        """Connect to Weaviate instance"""
        try:
            # Connect to Weaviate using basic client
            self.client = weaviate.Client(
                url=settings.weaviate_url,
                timeout_config=(5, 15)
            )

            # Test connection
            self.client.schema.get()

            # Create collection if it doesn't exist
            await self._ensure_collection_exists()

            logger.info("Connected to Weavia<PERSON> successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Weaviate: {str(e)}")
            raise Exception(f"Weaviate connection failed: {str(e)}")
    
    async def _ensure_collection_exists(self):
        """Ensure the CodeChunks collection exists"""
        try:
            # Check if class exists
            schema = self.client.schema.get()
            class_names = [cls['class'] for cls in schema.get('classes', [])]

            if self.collection_name not in class_names:
                # Create class schema
                class_schema = {
                    "class": self.collection_name,
                    "description": "Code chunks for semantic search",
                    "properties": [
                        {
                            "name": "analysis_id",
                            "dataType": ["int"],
                            "description": "ID of the analysis"
                        },
                        {
                            "name": "file_path",
                            "dataType": ["text"],
                            "description": "Path to the source file"
                        },
                        {
                            "name": "chunk_index",
                            "dataType": ["int"],
                            "description": "Index of the chunk within the file"
                        },
                        {
                            "name": "content",
                            "dataType": ["text"],
                            "description": "Code content of the chunk"
                        },
                        {
                            "name": "start_char",
                            "dataType": ["int"],
                            "description": "Start character position"
                        },
                        {
                            "name": "end_char",
                            "dataType": ["int"],
                            "description": "End character position"
                        },
                        {
                            "name": "language",
                            "dataType": ["text"],
                            "description": "Programming language"
                        },
                        {
                            "name": "created_at",
                            "dataType": ["text"],
                            "description": "Creation timestamp"
                        }
                    ],
                    "vectorizer": "none"  # No automatic vectorization
                }

                self.client.schema.create_class(class_schema)
                logger.info(f"Created Weaviate class: {self.collection_name}")
            else:
                logger.info(f"Weaviate class {self.collection_name} already exists")

        except Exception as e:
            logger.error(f"Error ensuring class exists: {str(e)}")
            raise
    
    async def index_repository_code(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code into Weaviate
        
        Args:
            analysis_id: ID of the analysis
            repo_content: Repository content from analysis service
            
        Returns:
            Indexing result with metadata
        """
        try:
            logger.info(f"Starting Weaviate indexing for analysis {analysis_id}")
            
            # Get code samples
            code_samples = repo_content.get("code_samples", {})
            indexable_files = self._filter_indexable_files(code_samples)
            
            if not indexable_files:
                return {
                    "status": "completed",
                    "files_indexed": 0,
                    "chunks_created": 0,
                    "vector_db_id": f"analysis_{analysis_id}"
                }
            
            # Clear existing data for this analysis
            await self._clear_analysis_data(analysis_id)
            
            # Index files
            total_chunks = 0

            for file_path, content in indexable_files.items():
                chunks = self._create_chunks(analysis_id, file_path, content)

                # Batch insert chunks
                if chunks:
                    with self.client.batch as batch:
                        batch.batch_size = 100
                        for chunk in chunks:
                            batch.add_data_object(
                                data_object={
                                    "analysis_id": chunk["analysis_id"],
                                    "file_path": chunk["file_path"],
                                    "chunk_index": chunk["chunk_index"],
                                    "content": chunk["content"],
                                    "start_char": chunk["start_char"],
                                    "end_char": chunk["end_char"],
                                    "language": chunk["language"],
                                    "created_at": datetime.utcnow().isoformat()
                                },
                                class_name=self.collection_name
                            )

                    total_chunks += len(chunks)
                    logger.debug(f"Indexed {len(chunks)} chunks from {file_path}")
            
            result = {
                "status": "completed",
                "files_indexed": len(indexable_files),
                "chunks_created": total_chunks,
                "vector_db_id": f"analysis_{analysis_id}"
            }
            
            logger.info(f"Weaviate indexing completed for analysis {analysis_id}: {len(indexable_files)} files, {total_chunks} chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error indexing repository code: {str(e)}")
            raise Exception(f"Weaviate indexing failed: {str(e)}")
    
    async def search_similar_code(self, analysis_id: int, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar code chunks using keyword search

        Args:
            analysis_id: ID of the analysis to search within
            query: Search query
            limit: Maximum number of results

        Returns:
            List of similar code chunks
        """
        try:
            # Use keyword search since we don't have vectorization enabled
            where_filter = {
                "path": ["analysis_id"],
                "operator": "Equal",
                "valueInt": analysis_id
            }

            response = (
                self.client.query
                .get(self.collection_name, ["file_path", "content", "chunk_index", "start_char", "end_char", "language"])
                .with_bm25(query=query)
                .with_where(where_filter)
                .with_limit(limit)
                .do()
            )

            results = []
            if response.get("data") and response["data"].get("Get") and response["data"]["Get"].get(self.collection_name):
                for obj in response["data"]["Get"][self.collection_name]:
                    results.append({
                        "file_path": obj.get("file_path", ""),
                        "content": obj.get("content", ""),
                        "chunk_index": obj.get("chunk_index", 0),
                        "start_char": obj.get("start_char", 0),
                        "end_char": obj.get("end_char", 0),
                        "language": obj.get("language", ""),
                        "score": obj.get("_additional", {}).get("score", 0)
                    })

            return results

        except Exception as e:
            logger.error(f"Error searching similar code: {str(e)}")
            return []
    
    async def get_analysis_stats(self, analysis_id: int) -> Dict[str, Any]:
        """Get statistics about indexed code for an analysis"""
        try:
            where_filter = {
                "path": ["analysis_id"],
                "operator": "Equal",
                "valueInt": analysis_id
            }

            # Get all objects for this analysis
            response = (
                self.client.query
                .get(self.collection_name, ["file_path"])
                .with_where(where_filter)
                .with_limit(1000)  # Reasonable limit
                .do()
            )

            total_chunks = 0
            unique_files = set()

            if response.get("data") and response["data"].get("Get") and response["data"]["Get"].get(self.collection_name):
                objects = response["data"]["Get"][self.collection_name]
                total_chunks = len(objects)

                for obj in objects:
                    if obj.get("file_path"):
                        unique_files.add(obj["file_path"])

            return {
                "total_chunks": total_chunks,
                "unique_files": len(unique_files),
                "files": list(unique_files)
            }

        except Exception as e:
            logger.error(f"Error getting analysis stats: {str(e)}")
            return {"total_chunks": 0, "unique_files": 0, "files": []}
    
    async def _clear_analysis_data(self, analysis_id: int):
        """Clear existing data for an analysis"""
        try:
            where_filter = {
                "path": ["analysis_id"],
                "operator": "Equal",
                "valueInt": analysis_id
            }

            # Get all objects for this analysis to delete them
            response = (
                self.client.query
                .get(self.collection_name)
                .with_where(where_filter)
                .with_additional(["id"])
                .with_limit(1000)
                .do()
            )

            if response.get("data") and response["data"].get("Get") and response["data"]["Get"].get(self.collection_name):
                objects = response["data"]["Get"][self.collection_name]

                # Delete each object
                for obj in objects:
                    if obj.get("_additional") and obj["_additional"].get("id"):
                        self.client.data_object.delete(
                            uuid=obj["_additional"]["id"],
                            class_name=self.collection_name
                        )

            logger.debug(f"Cleared existing data for analysis {analysis_id}")

        except Exception as e:
            logger.warning(f"Error clearing analysis data: {str(e)}")
    
    def _filter_indexable_files(self, code_samples: Dict[str, str]) -> Dict[str, str]:
        """Filter files that should be indexed"""
        indexable = {}
        
        # File extensions to index
        indexable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.r', '.sql',
            '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg', '.conf'
        }
        
        # Files to skip
        skip_patterns = [
            'node_modules/', 'venv/', '.git/', '__pycache__/', '.pytest_cache/',
            'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
            'package-lock.json', 'yarn.lock', 'poetry.lock', 'Pipfile.lock'
        ]
        
        for file_path, content in code_samples.items():
            # Skip if not string content
            if not isinstance(content, str):
                continue
            
            # Skip large files (>100KB)
            if len(content) > 100000:
                continue
            
            # Skip files in excluded directories
            if any(pattern in file_path for pattern in skip_patterns):
                continue
            
            # Check file extension
            file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
            if file_ext.lower() in indexable_extensions:
                indexable[file_path] = content
        
        return indexable
    
    def _create_chunks(self, analysis_id: int, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Create chunks from file content"""
        chunks = []
        
        # Determine language from file extension
        language = self._detect_language(file_path)
        
        # Split content into chunks
        for i in range(0, len(content), self.max_chunk_size):
            chunk_content = content[i:i + self.max_chunk_size]
            
            chunk = {
                "analysis_id": analysis_id,
                "file_path": file_path,
                "chunk_index": len(chunks),
                "content": chunk_content,
                "start_char": i,
                "end_char": min(i + self.max_chunk_size, len(content)),
                "language": language
            }
            
            chunks.append(chunk)
        
        return chunks
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension"""
        ext_to_lang = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.go': 'go',
            '.rs': 'rust',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.clj': 'clojure',
            '.hs': 'haskell',
            '.ml': 'ocaml',
            '.r': 'r',
            '.sql': 'sql',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.json': 'json',
            '.xml': 'xml',
            '.toml': 'toml',
            '.ini': 'ini',
            '.cfg': 'config',
            '.conf': 'config'
        }
        
        file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
        return ext_to_lang.get(file_ext.lower(), 'text')
