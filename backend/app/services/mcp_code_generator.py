"""
MCP Server Code Generation Service
Generates production-ready MCP servers based on selected tools and repository analysis
"""
import json
import logging
import zipfile
import tempfile
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
from openai import AsyncOpenAI
from ..config import settings

logger = logging.getLogger(__name__)


class MCPCodeGeneratorService:
    """Service for generating complete MCP server code based on selected tools"""
    
    def __init__(self):
        self.openai_client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.supported_languages = ["typescript", "javascript", "python", "go", "rust", "java", "csharp"]
        self.supported_architectures = ["http-sse", "websocket", "stdio", "docker"]
        
    async def generate_mcp_server(
        self,
        selected_tools: List[Dict[str, Any]],
        target_language: str,
        hosting_architecture: str,
        repo_context: Dict[str, Any],
        analysis_data: Dict[str, Any],
        customization_options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Generate complete MCP server code based on selected tools
        """
        if target_language not in self.supported_languages:
            raise ValueError(f"Unsupported language: {target_language}. Supported: {self.supported_languages}")
        
        if hosting_architecture not in self.supported_architectures:
            raise ValueError(f"Unsupported architecture: {hosting_architecture}. Supported: {self.supported_architectures}")
        
        if not settings.openai_api_key:
            raise ValueError("OpenAI API key not configured")
        
        try:
            logger.info(f"Generating MCP server in {target_language} with {len(selected_tools)} tools")
            
            # Prepare generation context
            generation_context = {
                "selected_tools": selected_tools,
                "target_language": target_language,
                "hosting_architecture": hosting_architecture,
                "repository_info": repo_context,
                "business_analysis": analysis_data.get("comprehensive_analysis", {}),
                "customization_options": customization_options or {}
            }
            
            # Generate the complete MCP server project
            if target_language == "python":
                generated_project = await self._generate_python_mcp_server(generation_context)
            elif target_language in ["typescript", "javascript"]:
                generated_project = await self._generate_node_mcp_server(generation_context)
            elif target_language == "go":
                generated_project = await self._generate_go_mcp_server(generation_context)
            elif target_language == "rust":
                generated_project = await self._generate_rust_mcp_server(generation_context)
            elif target_language == "java":
                generated_project = await self._generate_java_mcp_server(generation_context)
            elif target_language == "csharp":
                generated_project = await self._generate_csharp_mcp_server(generation_context)
            else:
                raise ValueError(f"Generation not implemented for {target_language}")
            
            # Create ZIP file with generated code
            zip_file_path = await self._create_project_zip(generated_project, target_language)
            
            return {
                "success": True,
                "language": target_language,
                "tools_count": len(selected_tools),
                "zip_file_path": zip_file_path,
                "project_structure": list(generated_project.keys()),
                "setup_instructions": generated_project.get("README.md", ""),
                "estimated_setup_time": self._estimate_setup_time(selected_tools, target_language)
            }
            
        except Exception as e:
            logger.error(f"MCP server generation failed: {str(e)}")
            raise Exception(f"Code generation failed: {str(e)}")
    
    async def _generate_python_mcp_server(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate Python MCP server project"""
        
        selected_tools = context["selected_tools"]
        repo_info = context["repository_info"]
        business_analysis = context["business_analysis"]
        options = context["customization_options"]
        
        generation_prompt = f"""
        Generate a complete, production-ready Python MCP server project based on these specifications:

        Repository Context:
        {json.dumps(repo_info, indent=2)}

        Business Analysis:
        {json.dumps(business_analysis, indent=2)}

        Selected Tools to Implement:
        {json.dumps(selected_tools, indent=2)}

        Customization Options:
        {json.dumps(options, indent=2)}

        Requirements:
        1. Use the official @modelcontextprotocol/server-python package
        2. Implement all selected tools with proper error handling
        3. Include comprehensive logging and monitoring
        4. Add configuration management using environment variables
        5. Include proper type hints and docstrings
        6. Add unit tests for each tool using pytest
        7. Include Docker configuration for deployment
        8. Add comprehensive README with setup instructions
        9. Include requirements.txt with all dependencies
        10. Follow Python best practices (PEP 8, etc.)

        Generate a complete project with these files:
        - main.py (MCP server entry point)
        - tools/ (directory with individual tool implementations)
        - config.py (configuration management)
        - requirements.txt (dependencies)
        - Dockerfile (containerization)
        - docker-compose.yml (local development)
        - tests/ (unit tests)
        - README.md (setup and usage instructions)
        - .env.example (environment variables template)
        - pyproject.toml (modern Python project configuration)

        For each selected tool, create:
        1. Proper input validation using Pydantic models
        2. Comprehensive error handling with meaningful messages
        3. Logging for debugging and monitoring
        4. Unit tests covering normal and edge cases
        5. Clear documentation with examples

        Make the server production-ready with:
        - Health check endpoints
        - Proper logging configuration
        - Error reporting
        - Resource management
        - Security best practices

        Return as JSON with this structure:
        {{
            "main.py": "file_content",
            "tools/__init__.py": "file_content",
            "tools/tool1.py": "file_content",
            "config.py": "file_content",
            "requirements.txt": "file_content",
            "Dockerfile": "file_content",
            "docker-compose.yml": "file_content",
            "tests/__init__.py": "file_content", 
            "tests/test_tools.py": "file_content",
            "README.md": "file_content",
            ".env.example": "file_content",
            "pyproject.toml": "file_content"
        }}

        Focus on creating maintainable, well-documented, production-ready code that follows MCP protocol specifications.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": generation_prompt}],
            temperature=0.1,
            max_tokens=8000
        )
        
        return self._parse_openai_response(response, "Python")
    
    async def _generate_node_mcp_server(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate Node.js/TypeScript MCP server project"""
        
        selected_tools = context["selected_tools"]
        repo_info = context["repository_info"]
        business_analysis = context["business_analysis"]
        options = context["customization_options"]
        language = context["target_language"]
        
        generation_prompt = f"""
        Generate a complete, production-ready {language.title()} MCP server project:

        Repository Context:
        {json.dumps(repo_info, indent=2)}

        Business Analysis:
        {json.dumps(business_analysis, indent=2)}

        Selected Tools to Implement:
        {json.dumps(selected_tools, indent=2)}

        Language: {language}

        Requirements:
        1. Use the official @modelcontextprotocol/sdk package
        2. {"Use TypeScript with proper types" if language == "typescript" else "Use modern JavaScript (ES2022+)"}
        3. Implement all selected tools with comprehensive error handling
        4. Include proper logging with winston or similar
        5. Add configuration management using dotenv
        6. Include unit tests using Jest
        7. Add Docker configuration for deployment
        8. Include comprehensive README with setup instructions
        9. Use npm/yarn for package management
        10. Follow Node.js best practices

        Generate a complete project with these files:
        - {"index.ts" if language == "typescript" else "index.js"} (MCP server entry point)
        - src/tools/ (tool implementations)
        - src/config/ (configuration)
        - package.json (dependencies and scripts)
        - {"tsconfig.json" if language == "typescript" else ""} (TypeScript config)
        - Dockerfile (containerization)
        - docker-compose.yml (local development)
        - tests/ (unit tests)
        - jest.config.js (test configuration)
        - README.md (documentation)
        - .env.example (environment template)
        - .gitignore (git ignore rules)

        For each tool:
        1. {"Proper TypeScript interfaces for inputs/outputs" if language == "typescript" else "JSDoc for parameter documentation"}
        2. Comprehensive error handling
        3. Structured logging
        4. Unit tests with mocking
        5. Clear documentation

        Return as JSON with file paths as keys and content as values.
        Make it production-ready with health checks, monitoring, and security.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": generation_prompt}],
            temperature=0.1,
            max_tokens=8000
        )
        
        return self._parse_openai_response(response, language)
    
    async def _create_project_zip(self, project_files: Dict[str, str], language: str) -> str:
        """Create ZIP file containing the generated project"""
        
        # Create temporary directory for the project
        with tempfile.TemporaryDirectory() as temp_dir:
            project_dir = Path(temp_dir) / f"mcp-server-{language}"
            project_dir.mkdir(exist_ok=True)
            
            # Write all project files
            for file_path, content in project_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Write file content
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            # Create ZIP file
            zip_path = Path(temp_dir) / f"mcp-server-{language}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(project_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_path = file_path.relative_to(project_dir)
                        zipf.write(file_path, arc_path)
            
            # Move to permanent location
            permanent_zip_path = f"/tmp/mcp-server-{language}-{int(datetime.now().timestamp())}.zip"
            import shutil
            shutil.move(str(zip_path), permanent_zip_path)
            
            return permanent_zip_path
    
    def _estimate_setup_time(self, selected_tools: List[Dict[str, Any]], language: str) -> Dict[str, Any]:
        """Estimate setup and deployment time"""
        
        base_setup_time = {
            "python": 10,  # minutes
            "typescript": 15,
            "javascript": 12
        }
        
        tool_complexity_time = 0
        for tool in selected_tools:
            complexity = tool.get("complexity_level", "medium")
            if complexity == "low":
                tool_complexity_time += 2
            elif complexity == "medium":
                tool_complexity_time += 5
            else:  # high
                tool_complexity_time += 10
        
        total_setup_minutes = base_setup_time.get(language, 15) + tool_complexity_time
        
        return {
            "estimated_setup_minutes": total_setup_minutes,
            "base_setup": base_setup_time.get(language, 15),
            "tools_complexity_time": tool_complexity_time,
            "setup_difficulty": "easy" if total_setup_minutes <= 20 else "moderate" if total_setup_minutes <= 45 else "complex"
        }

    async def generate_tool_documentation(
        self, 
        selected_tools: List[Dict[str, Any]], 
        repo_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive documentation for selected MCP tools"""
        
        doc_prompt = f"""
        Generate comprehensive documentation for these MCP tools:

        Repository Context:
        {json.dumps(repo_context, indent=2)}

        Selected Tools:
        {json.dumps(selected_tools, indent=2)}

        Create documentation that includes:
        1. Overview of the MCP server and its purpose
        2. Installation and setup instructions
        3. Configuration options
        4. Detailed tool descriptions with examples
        5. API reference for each tool
        6. Common use cases and workflows
        7. Troubleshooting guide
        8. Integration examples with Claude Desktop

        Return as JSON:
        {{
            "overview": "server overview and purpose",
            "installation": "step-by-step setup instructions",
            "configuration": "configuration options and environment variables",
            "tools_reference": [
                {{
                    "name": "tool_name",
                    "description": "what it does",
                    "parameters": "input parameters",
                    "returns": "output format",
                    "examples": ["example usage"]
                }}
            ],
            "use_cases": [
                {{
                    "scenario": "use case description",
                    "workflow": "step by step workflow",
                    "example": "concrete example"
                }}
            ],
            "troubleshooting": [
                {{
                    "issue": "common problem",
                    "solution": "how to fix it"
                }}
            ],
            "integration_examples": {{
                "claude_desktop": "how to integrate with Claude Desktop",
                "vscode": "VS Code integration example",
                "api_usage": "programmatic API usage"
            }}
        }}
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": doc_prompt}],
            temperature=0.2,
            max_tokens=3000
        )
        
        return self._parse_openai_response(response, "Go")

    async def _generate_go_mcp_server(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate Go MCP server with excellent performance characteristics"""
        
        selected_tools = context["selected_tools"]
        hosting_architecture = context["hosting_architecture"]
        repo_info = context["repository_info"]
        business_analysis = context["business_analysis"]
        options = context["customization_options"]
        
        generation_prompt = f"""
        Generate a complete, production-ready Go MCP server project optimized for {hosting_architecture} architecture:

        Repository Context: {json.dumps(repo_info, indent=2)}
        Selected Tools: {json.dumps(selected_tools, indent=2)}
        Hosting: {hosting_architecture}

        Requirements:
        1. Use Go 1.21+ with modern Go modules
        2. Implement JSON-RPC 2.0 protocol for MCP
        3. {self._get_architecture_requirements(hosting_architecture)}
        4. Include comprehensive error handling and logging
        5. Add configuration management with environment variables
        6. Include unit and integration tests
        7. Add Docker configuration for containerization
        8. Include proper middleware for authentication and rate limiting
        9. Use structured logging with slog
        10. Follow Go best practices and idioms

        Generate project with:
        - main.go (server entry point)
        - internal/ (core server logic)
        - pkg/tools/ (tool implementations)
        - pkg/config/ (configuration)
        - go.mod and go.sum
        - Dockerfile and docker-compose.yml
        - tests/ (test files)
        - README.md
        - .env.example

        Provide as JSON with filename -> content mapping.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": generation_prompt}],
            temperature=0.2,
            max_tokens=4000
        )
        
        return self._parse_openai_response(response, "Go")

    async def _generate_rust_mcp_server(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate Rust MCP server with maximum performance and safety"""
        
        selected_tools = context["selected_tools"]
        hosting_architecture = context["hosting_architecture"]
        repo_info = context["repository_info"]
        
        generation_prompt = f"""
        Generate a complete, production-ready Rust MCP server project optimized for {hosting_architecture}:

        Repository Context: {json.dumps(repo_info, indent=2)}
        Selected Tools: {json.dumps(selected_tools, indent=2)}
        Hosting: {hosting_architecture}

        Requirements:
        1. Use Rust 2021 edition with latest stable toolchain
        2. Implement JSON-RPC 2.0 with serde and tokio
        3. {self._get_architecture_requirements(hosting_architecture)}
        4. Include comprehensive error handling with thiserror/anyhow
        5. Use tokio for async runtime and reqwest for HTTP
        6. Add structured configuration with figment/config
        7. Include unit and integration tests
        8. Add Docker multi-stage build
        9. Use tracing for structured logging
        10. Follow Rust best practices and clippy lints

        Generate project with:
        - src/main.rs
        - src/lib.rs
        - src/tools/ (tool implementations)
        - src/config.rs
        - Cargo.toml
        - Dockerfile
        - tests/
        - README.md

        Provide as JSON with filename -> content mapping.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": generation_prompt}],
            temperature=0.2,
            max_tokens=4000
        )
        
        return self._parse_openai_response(response, "Go")

    async def _generate_java_mcp_server(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate Java MCP server for enterprise environments"""
        
        selected_tools = context["selected_tools"]
        hosting_architecture = context["hosting_architecture"]
        repo_info = context["repository_info"]
        
        generation_prompt = f"""
        Generate a complete, enterprise-ready Java MCP server project for {hosting_architecture}:

        Repository Context: {json.dumps(repo_info, indent=2)}
        Selected Tools: {json.dumps(selected_tools, indent=2)}
        Hosting: {hosting_architecture}

        Requirements:
        1. Use Java 17+ with Spring Boot 3.x
        2. Implement JSON-RPC 2.0 with Jackson
        3. {self._get_architecture_requirements(hosting_architecture)}
        4. Include comprehensive validation with Bean Validation
        5. Add security with Spring Security
        6. Use Micrometer for metrics and observability
        7. Include JUnit 5 tests with Testcontainers
        8. Add Docker and Kubernetes manifests
        9. Use SLF4J with Logback for logging
        10. Follow Java enterprise patterns

        Generate Maven project with:
        - pom.xml
        - src/main/java/ (source code)
        - src/main/resources/ (config files)
        - src/test/java/ (tests)
        - Dockerfile
        - k8s/ (Kubernetes manifests)
        - README.md

        Provide as JSON with filename -> content mapping.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": generation_prompt}],
            temperature=0.2,
            max_tokens=4000
        )
        
        return self._parse_openai_response(response, "Go")

    async def _generate_csharp_mcp_server(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate C#/.NET MCP server for Windows environments"""
        
        selected_tools = context["selected_tools"]
        hosting_architecture = context["hosting_architecture"]
        repo_info = context["repository_info"]
        
        generation_prompt = f"""
        Generate a complete, production-ready C#/.NET MCP server project for {hosting_architecture}:

        Repository Context: {json.dumps(repo_info, indent=2)}
        Selected Tools: {json.dumps(selected_tools, indent=2)}
        Hosting: {hosting_architecture}

        Requirements:
        1. Use .NET 8.0 with ASP.NET Core
        2. Implement JSON-RPC 2.0 with System.Text.Json
        3. {self._get_architecture_requirements(hosting_architecture)}
        4. Include comprehensive validation with FluentValidation
        5. Add authentication/authorization
        6. Use Serilog for structured logging
        7. Include xUnit tests with WebApplicationFactory
        8. Add Docker support
        9. Use dependency injection and configuration patterns
        10. Follow .NET best practices and nullable reference types

        Generate project with:
        - *.csproj files
        - Program.cs and Startup.cs
        - Controllers/ and Services/
        - Models/ and DTOs/
        - Tests/
        - Dockerfile
        - appsettings.json
        - README.md

        Provide as JSON with filename -> content mapping.
        """
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": generation_prompt}],
            temperature=0.2,
            max_tokens=4000
        )
        
        return self._parse_openai_response(response, "Go")

    def _parse_openai_response(self, response, language: str) -> Dict[str, str]:
        """Helper method to parse OpenAI response with proper error handling"""
        try:
            content = response.choices[0].message.content
            logger.info(f"Generated {language} project content length: {len(content) if content else 0}")
            if not content:
                logger.error(f"OpenAI returned empty content for {language} project generation")
                raise Exception("OpenAI returned empty response")
            
            # Log first 500 chars for debugging
            logger.info(f"Generated {language} content preview: {content[:500]}...")
            
            # Extract JSON from markdown code blocks if present
            json_content = self._extract_json_from_markdown(content)
            
            return json.loads(json_content)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse generated {language} project JSON: {str(e)}")
            logger.error(f"Raw content: {content[:1000] if content else 'None'}...")
            raise Exception(f"Failed to generate valid {language} project structure")
        except Exception as e:
            logger.error(f"OpenAI API error during {language} generation: {str(e)}")
            raise Exception(f"Code generation failed: {str(e)}")
    
    def _extract_json_from_markdown(self, content: str) -> str:
        """Extract JSON content from markdown code blocks"""
        import re
        
        # Try to find JSON content within markdown code blocks
        # Pattern matches ```json\n{...}\n``` or ```\n{...}\n```
        json_pattern = r'```(?:json)?\s*\n(.*?)\n```'
        match = re.search(json_pattern, content, re.DOTALL)
        
        if match:
            logger.info("Found JSON content within markdown code blocks")
            return match.group(1).strip()
        
        # If no code blocks found, try to find JSON object directly
        # Look for content that starts with { and ends with }
        json_object_pattern = r'\{.*\}'
        match = re.search(json_object_pattern, content, re.DOTALL)
        
        if match:
            logger.info("Found JSON object in content")
            return match.group(0).strip()
        
        # If still no JSON found, return original content
        logger.warning("No JSON pattern found, returning original content")
        return content

    def _get_architecture_requirements(self, architecture: str) -> str:
        """Get specific requirements based on hosting architecture"""
        
        requirements = {
            "http-sse": "Implement HTTP REST endpoints with Server-Sent Events for real-time communication. Include proper CORS handling and connection management.",
            "websocket": "Implement WebSocket server with proper connection lifecycle management, heartbeat/ping-pong, and reconnection handling.",
            "stdio": "Implement stdin/stdout communication with proper process lifecycle management and signal handling.",
            "docker": "Include comprehensive Docker configuration with multi-stage builds, health checks, and proper security practices."
        }
        
        return requirements.get(architecture, "Implement standard MCP protocol communication.")


# Import datetime for timestamp generation
from datetime import datetime