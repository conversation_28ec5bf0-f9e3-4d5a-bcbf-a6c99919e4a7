"""
Code Indexing Service

Handles indexing of repository code for better context understanding.
Provides progress tracking and re-indexing capabilities.
"""

import asyncio
import hashlib
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.analysis import RepoAnalysis
from .weaviate_service import WeaviateService

logger = logging.getLogger(__name__)


class IndexingService:
    """Service for indexing repository code with progress tracking"""
    
    def __init__(self):
        self.chunk_size = 1000  # Characters per chunk for indexing
        self.max_file_size = 100000  # Skip files larger than 100KB
    
    async def index_repository(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code with progress tracking
        
        Args:
            analysis_id: ID of the analysis record
            repo_content: Repository content from analysis service
            
        Returns:
            Indexing result with status and metadata
        """
        logger.info(f"Starting code indexing for analysis {analysis_id}")
        
        try:
            # Update status to indexing
            await self._update_indexing_status(analysis_id, "indexing", 0)
            
            # Calculate code hash for change detection
            code_hash = self._calculate_code_hash(repo_content)
            
            # Get files to index
            code_samples = repo_content.get("code_samples", {})
            indexable_files = self._filter_indexable_files(code_samples)
            
            total_files = len(indexable_files)
            if total_files == 0:
                await self._update_indexing_status(analysis_id, "completed", 100)
                return {
                    "status": "completed",
                    "files_indexed": 0,
                    "total_chunks": 0,
                    "code_hash": code_hash
                }
            
            # Index files with progress updates using Weaviate
            async with WeaviateService() as weaviate:
                weaviate_result = await weaviate.index_repository_code(analysis_id, repo_content)
                total_chunks = weaviate_result.get("chunks_created", 0)
                indexed_files = weaviate_result.get("files_indexed", 0)

                # Update progress incrementally for better UX
                for progress in range(10, 101, 10):
                    await self._update_indexing_status(analysis_id, "indexing", progress)
                    await asyncio.sleep(0.2)  # Small delay to make progress visible
            
            # Mark as completed
            await self._update_indexing_status(analysis_id, "completed", 100, code_hash)
            
            result = {
                "status": "completed",
                "files_indexed": indexed_files,
                "total_chunks": total_chunks,
                "code_hash": code_hash,
                "indexed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Indexing completed for analysis {analysis_id}: {indexed_files} files, {total_chunks} chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error indexing repository for analysis {analysis_id}: {str(e)}")
            await self._update_indexing_status(analysis_id, "failed", 0)
            raise Exception(f"Code indexing failed: {str(e)}")
    
    async def should_reindex(self, analysis_id: int, repo_content: Dict[str, Any]) -> bool:
        """
        Check if repository should be re-indexed based on code changes
        
        Args:
            analysis_id: ID of the analysis record
            repo_content: Current repository content
            
        Returns:
            True if re-indexing is needed
        """
        try:
            # Get current analysis record
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if not analysis or not analysis.code_hash:
                return True  # No previous indexing, should index
            
            # Calculate current code hash
            current_hash = self._calculate_code_hash(repo_content)
            
            # Compare with stored hash
            return analysis.code_hash != current_hash
            
        except Exception as e:
            logger.error(f"Error checking reindex status: {str(e)}")
            return True  # Default to reindex on error
    
    async def get_indexing_status(self, analysis_id: int) -> Dict[str, Any]:
        """Get current indexing status for an analysis"""
        try:
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if not analysis:
                return {"status": "not_found"}
            
            return {
                "status": analysis.indexing_status,
                "progress": analysis.indexing_progress,
                "last_indexed_at": analysis.last_indexed_at.isoformat() if analysis.last_indexed_at else None,
                "vector_db_id": analysis.vector_db_id
            }
            
        except Exception as e:
            logger.error(f"Error getting indexing status: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_code_hash(self, repo_content: Dict[str, Any]) -> str:
        """Calculate hash of repository code for change detection"""
        try:
            code_samples = repo_content.get("code_samples", {})
            
            # Create a deterministic string from all code content
            code_string = ""
            for file_path in sorted(code_samples.keys()):
                content = code_samples[file_path]
                if isinstance(content, str):
                    code_string += f"{file_path}:{content}\n"
            
            # Calculate SHA-256 hash
            return hashlib.sha256(code_string.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating code hash: {str(e)}")
            return "error"
    
    def _filter_indexable_files(self, code_samples: Dict[str, str]) -> Dict[str, str]:
        """Filter files that should be indexed"""
        indexable = {}
        
        # File extensions to index
        indexable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.r', '.sql',
            '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg', '.conf'
        }
        
        # Files to skip
        skip_patterns = [
            'node_modules/', 'venv/', '.git/', '__pycache__/', '.pytest_cache/',
            'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
            'package-lock.json', 'yarn.lock', 'poetry.lock', 'Pipfile.lock'
        ]
        
        for file_path, content in code_samples.items():
            # Skip if not string content
            if not isinstance(content, str):
                continue
            
            # Skip large files
            if len(content) > self.max_file_size:
                continue
            
            # Skip files in excluded directories
            if any(pattern in file_path for pattern in skip_patterns):
                continue
            
            # Check file extension
            file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
            if file_ext.lower() in indexable_extensions:
                indexable[file_path] = content
        
        return indexable
    
    async def _index_file_content(self, analysis_id: int, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Index content of a single file into chunks"""
        chunks = []
        
        # Split content into chunks
        for i in range(0, len(content), self.chunk_size):
            chunk_content = content[i:i + self.chunk_size]
            
            chunk = {
                "analysis_id": analysis_id,
                "file_path": file_path,
                "chunk_index": len(chunks),
                "content": chunk_content,
                "start_char": i,
                "end_char": min(i + self.chunk_size, len(content))
            }
            
            chunks.append(chunk)
        
        # In a real implementation, you would store these chunks in a vector database
        # For now, we'll just simulate the indexing process
        logger.debug(f"Indexed {len(chunks)} chunks from {file_path}")
        
        return chunks
    
    async def _update_indexing_status(self, analysis_id: int, status: str, 
                                    progress: int, code_hash: Optional[str] = None):
        """Update indexing status in database"""
        try:
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if analysis:
                analysis.indexing_status = status
                analysis.indexing_progress = progress
                
                if status == "completed":
                    analysis.last_indexed_at = datetime.utcnow()
                    if code_hash:
                        analysis.code_hash = code_hash
                
                db.commit()
                logger.debug(f"Updated indexing status for analysis {analysis_id}: {status} ({progress}%)")
            
        except Exception as e:
            logger.error(f"Error updating indexing status: {str(e)}")
    
    async def trigger_reindex(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Trigger re-indexing of repository code"""
        logger.info(f"Triggering re-index for analysis {analysis_id}")
        
        # Reset indexing status
        await self._update_indexing_status(analysis_id, "not_started", 0)
        
        # Start indexing
        return await self.index_repository(analysis_id, repo_content)
