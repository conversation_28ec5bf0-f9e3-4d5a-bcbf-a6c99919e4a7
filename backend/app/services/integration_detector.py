"""
Integration Detection Service

Detects third-party integrations in repositories and suggests MCP alternatives.
This service analyzes package dependencies, code patterns, and environment variables
to identify heavyweight integrations that could be replaced with lightweight MCPs.
"""

import re
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class DetectedIntegration:
    """Represents a detected third-party integration"""
    integration_type: str  # payment, email, sms, cloud, database, etc.
    service_name: str      # stripe, sendgrid, twilio, aws, etc.
    detection_method: str  # package, code_pattern, env_var, config
    confidence: float      # 0.0 to 1.0
    file_locations: List[str]
    package_names: List[str]
    code_patterns: List[str]
    env_variables: List[str]
    migration_complexity: str  # low, medium, high
    description: str


@dataclass
class MCPAlternative:
    """Represents an MCP alternative for an integration"""
    mcp_name: str
    mcp_url: str
    description: str
    benefits: List[str]
    migration_effort: str
    compatibility_score: float


class IntegrationDetector:
    """Service for detecting third-party integrations in repositories"""
    
    def __init__(self):
        self.integration_patterns = self._load_integration_patterns()
        self.mcp_alternatives = self._load_mcp_alternatives()
    
    def _load_integration_patterns(self) -> Dict[str, Any]:
        """Load integration detection patterns"""
        return {
            "payment": {
                "packages": {
                    "stripe": {"confidence": 0.95, "complexity": "medium"},
                    "paypal-sdk": {"confidence": 0.90, "complexity": "high"},
                    "square": {"confidence": 0.85, "complexity": "medium"},
                    "braintree": {"confidence": 0.85, "complexity": "high"},
                    "razorpay": {"confidence": 0.80, "complexity": "medium"}
                },
                "code_patterns": [
                    r"stripe\.Charge\.create",
                    r"stripe\.PaymentIntent",
                    r"paypal\.Payment",
                    r"square\.payments",
                    r"braintree\.Transaction"
                ],
                "env_patterns": [
                    r"STRIPE_.*_KEY",
                    r"PAYPAL_.*_ID",
                    r"SQUARE_.*_ID",
                    r"BRAINTREE_.*_KEY"
                ],
                "description": "Payment processing integration"
            },
            "email": {
                "packages": {
                    "sendgrid": {"confidence": 0.95, "complexity": "low"},
                    "mailgun": {"confidence": 0.90, "complexity": "low"},
                    "ses-boto3": {"confidence": 0.85, "complexity": "medium"},
                    "postmark": {"confidence": 0.80, "complexity": "low"},
                    "mailchimp": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"sendgrid\.SendGridAPIClient",
                    r"mailgun\.Mailgun",
                    r"boto3\.client\(['\"]ses['\"]",
                    r"postmark\.PMMail",
                    r"mailchimp3\.MailChimp"
                ],
                "env_patterns": [
                    r"SENDGRID_API_KEY",
                    r"MAILGUN_.*_KEY",
                    r"AWS_SES_.*",
                    r"POSTMARK_.*_TOKEN",
                    r"MAILCHIMP_API_KEY"
                ],
                "description": "Email service integration"
            },
            "sms": {
                "packages": {
                    "twilio": {"confidence": 0.95, "complexity": "medium"},
                    "nexmo": {"confidence": 0.85, "complexity": "medium"},
                    "plivo": {"confidence": 0.80, "complexity": "medium"},
                    "messagebird": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"twilio\.rest\.Client",
                    r"nexmo\.Client",
                    r"plivo\.RestClient",
                    r"messagebird\.Client"
                ],
                "env_patterns": [
                    r"TWILIO_.*_SID",
                    r"NEXMO_API_KEY",
                    r"PLIVO_.*_ID",
                    r"MESSAGEBIRD_.*_KEY"
                ],
                "description": "SMS/Communication service integration"
            },
            "cloud_storage": {
                "packages": {
                    "boto3": {"confidence": 0.90, "complexity": "high"},
                    "google-cloud-storage": {"confidence": 0.90, "complexity": "high"},
                    "azure-storage": {"confidence": 0.85, "complexity": "high"},
                    "dropbox": {"confidence": 0.80, "complexity": "medium"}
                },
                "code_patterns": [
                    r"boto3\.client\(['\"]s3['\"]",
                    r"storage\.Client\(\)",
                    r"BlobServiceClient",
                    r"dropbox\.Dropbox"
                ],
                "env_patterns": [
                    r"AWS_.*_KEY",
                    r"GOOGLE_CLOUD_.*",
                    r"AZURE_STORAGE_.*",
                    r"DROPBOX_.*_TOKEN"
                ],
                "description": "Cloud storage integration"
            },
            "database": {
                "packages": {
                    "redis": {"confidence": 0.85, "complexity": "low"},
                    "pymongo": {"confidence": 0.85, "complexity": "medium"},
                    "elasticsearch": {"confidence": 0.80, "complexity": "high"},
                    "psycopg2": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"redis\.Redis",
                    r"pymongo\.MongoClient",
                    r"elasticsearch\.Elasticsearch",
                    r"psycopg2\.connect"
                ],
                "env_patterns": [
                    r"REDIS_URL",
                    r"MONGODB_.*",
                    r"ELASTICSEARCH_.*",
                    r"DATABASE_URL"
                ],
                "description": "Database service integration"
            },
            "analytics": {
                "packages": {
                    "mixpanel": {"confidence": 0.85, "complexity": "low"},
                    "segment-analytics": {"confidence": 0.80, "complexity": "low"},
                    "google-analytics": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"mixpanel\.Mixpanel",
                    r"analytics\.track",
                    r"ga\(['\"]send['\"]"
                ],
                "env_patterns": [
                    r"MIXPANEL_.*_TOKEN",
                    r"SEGMENT_.*_KEY",
                    r"GA_TRACKING_ID"
                ],
                "description": "Analytics service integration"
            },
            "ai_chat": {
                "packages": {
                    "openai": {"confidence": 0.95, "complexity": "medium"},
                    "anthropic": {"confidence": 0.95, "complexity": "medium"},
                    "langchain": {"confidence": 0.90, "complexity": "high"},
                    "transformers": {"confidence": 0.85, "complexity": "high"}
                },
                "code_patterns": [
                    r"openai\.ChatCompletion",
                    r"anthropic\.Anthropic",
                    r"langchain\.llms",
                    r"AutoTokenizer\.from_pretrained"
                ],
                "env_patterns": [
                    r"OPENAI_API_KEY",
                    r"ANTHROPIC_API_KEY",
                    r"HUGGINGFACE_.*_TOKEN"
                ],
                "description": "AI/Chat service integration"
            },
            "development_tools": {
                "packages": {
                    "github": {"confidence": 0.90, "complexity": "medium"},
                    "gitlab": {"confidence": 0.85, "complexity": "medium"},
                    "jira": {"confidence": 0.80, "complexity": "high"},
                    "slack-sdk": {"confidence": 0.85, "complexity": "low"},
                    "notion-client": {"confidence": 0.80, "complexity": "medium"}
                },
                "code_patterns": [
                    r"github\.Github",
                    r"gitlab\.Gitlab",
                    r"jira\.JIRA",
                    r"slack_sdk\.WebClient",
                    r"notion_client\.Client"
                ],
                "env_patterns": [
                    r"GITHUB_.*_TOKEN",
                    r"GITLAB_.*_TOKEN",
                    r"JIRA_.*_TOKEN",
                    r"SLACK_.*_TOKEN",
                    r"NOTION_.*_TOKEN"
                ],
                "description": "Development and productivity tools integration"
            },
            "monitoring": {
                "packages": {
                    "sentry-sdk": {"confidence": 0.90, "complexity": "low"},
                    "datadog": {"confidence": 0.85, "complexity": "medium"},
                    "newrelic": {"confidence": 0.80, "complexity": "medium"},
                    "prometheus-client": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"sentry_sdk\.init",
                    r"datadog\.initialize",
                    r"newrelic\.agent",
                    r"prometheus_client\.Counter"
                ],
                "env_patterns": [
                    r"SENTRY_DSN",
                    r"DATADOG_API_KEY",
                    r"NEW_RELIC_.*_KEY",
                    r"PROMETHEUS_.*"
                ],
                "description": "Monitoring and observability integration"
            },
            "design_tools": {
                "packages": {
                    "figma-api": {"confidence": 0.85, "complexity": "medium"},
                    "sketch-api": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"figma\.Api",
                    r"sketch\.Document"
                ],
                "env_patterns": [
                    r"FIGMA_.*_TOKEN",
                    r"SKETCH_.*_TOKEN"
                ],
                "description": "Design tools integration"
            },
            "ticketing": {
                "packages": {
                    "linear-sdk": {"confidence": 0.85, "complexity": "medium"},
                    "zendesk": {"confidence": 0.80, "complexity": "medium"},
                    "freshdesk": {"confidence": 0.75, "complexity": "medium"}
                },
                "code_patterns": [
                    r"linear\.LinearClient",
                    r"zendesk\.Zendesk",
                    r"freshdesk\.API"
                ],
                "env_patterns": [
                    r"LINEAR_API_KEY",
                    r"ZENDESK_.*_TOKEN",
                    r"FRESHDESK_.*_KEY"
                ],
                "description": "Ticketing and support tools integration"
            },
            "scraping_search": {
                "packages": {
                    "scrapy": {"confidence": 0.85, "complexity": "high"},
                    "beautifulsoup4": {"confidence": 0.80, "complexity": "medium"},
                    "selenium": {"confidence": 0.85, "complexity": "high"},
                    "tavily-python": {"confidence": 0.90, "complexity": "low"}
                },
                "code_patterns": [
                    r"scrapy\.Spider",
                    r"BeautifulSoup",
                    r"webdriver\.Chrome",
                    r"tavily\.TavilyClient"
                ],
                "env_patterns": [
                    r"SCRAPY_.*",
                    r"SELENIUM_.*",
                    r"TAVILY_API_KEY"
                ],
                "description": "Web scraping and search integration"
            }
        }

    def _load_mcp_alternatives(self) -> Dict[str, List[MCPAlternative]]:
        """Load MCP alternatives for detected integrations based on MCP Market Map"""
        return {
            # Payment integrations
            "stripe": [
                MCPAlternative(
                    mcp_name="stripe-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/stripe",
                    description="Official Stripe MCP server for payment processing",
                    benefits=["Standardized interface", "Better error handling", "Reduced complexity"],
                    migration_effort="medium",
                    compatibility_score=0.9
                )
            ],

            # Email integrations
            "sendgrid": [
                MCPAlternative(
                    mcp_name="resend-mcp",
                    mcp_url="https://github.com/resend/mcp-server",
                    description="Resend MCP server for modern email delivery",
                    benefits=["Modern API", "Better deliverability", "Developer-friendly"],
                    migration_effort="low",
                    compatibility_score=0.85
                )
            ],
            "mailgun": [
                MCPAlternative(
                    mcp_name="resend-mcp",
                    mcp_url="https://github.com/resend/mcp-server",
                    description="Resend MCP server as Mailgun alternative",
                    benefits=["Simpler API", "Better pricing", "Modern interface"],
                    migration_effort="medium",
                    compatibility_score=0.8
                )
            ],

            # Database integrations
            "redis": [
                MCPAlternative(
                    mcp_name="sqlite-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite",
                    description="SQLite MCP server for local database operations",
                    benefits=["No external dependencies", "File-based storage", "ACID compliance"],
                    migration_effort="high",
                    compatibility_score=0.6
                )
            ],
            "pymongo": [
                MCPAlternative(
                    mcp_name="postgres-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/postgres",
                    description="PostgreSQL MCP server with JSON support",
                    benefits=["ACID compliance", "JSON support", "Better performance"],
                    migration_effort="high",
                    compatibility_score=0.7
                )
            ],

            # Cloud storage
            "boto3": [
                MCPAlternative(
                    mcp_name="filesystem-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem",
                    description="Local filesystem MCP server",
                    benefits=["No cloud dependencies", "Faster access", "Cost effective"],
                    migration_effort="high",
                    compatibility_score=0.5
                )
            ],

            # Development tools
            "github": [
                MCPAlternative(
                    mcp_name="github-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/github",
                    description="Official GitHub MCP server",
                    benefits=["Standardized interface", "Better rate limiting", "Enhanced security"],
                    migration_effort="low",
                    compatibility_score=0.95
                )
            ],
            "slack-sdk": [
                MCPAlternative(
                    mcp_name="slack-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/slack",
                    description="Slack MCP server for team communication",
                    benefits=["Simplified API", "Better error handling", "Event streaming"],
                    migration_effort="low",
                    compatibility_score=0.9
                )
            ],
            "notion-client": [
                MCPAlternative(
                    mcp_name="notion-mcp",
                    mcp_url="https://github.com/modelcontextprotocol/servers/tree/main/src/notion",
                    description="Notion MCP server for knowledge management",
                    benefits=["Unified interface", "Better pagination", "Rich content support"],
                    migration_effort="medium",
                    compatibility_score=0.85
                )
            ],

            # Monitoring
            "sentry-sdk": [
                MCPAlternative(
                    mcp_name="sentry-mcp",
                    mcp_url="https://github.com/getsentry/mcp-server",
                    description="Sentry MCP server for error tracking",
                    benefits=["Enhanced debugging", "Better context", "Streamlined workflow"],
                    migration_effort="low",
                    compatibility_score=0.9
                )
            ],

            # Search and scraping
            "tavily-python": [
                MCPAlternative(
                    mcp_name="tavily-mcp",
                    mcp_url="https://github.com/tavily-ai/mcp-server",
                    description="Tavily MCP server for AI-powered search",
                    benefits=["AI-optimized results", "Better context", "Reduced API calls"],
                    migration_effort="low",
                    compatibility_score=0.95
                )
            ],
            "beautifulsoup4": [
                MCPAlternative(
                    mcp_name="firecrawl-mcp",
                    mcp_url="https://github.com/firecrawl/mcp-server",
                    description="Firecrawl MCP server for web scraping",
                    benefits=["Better parsing", "Anti-bot protection", "Structured output"],
                    migration_effort="medium",
                    compatibility_score=0.8
                )
            ],
            "selenium": [
                MCPAlternative(
                    mcp_name="browserbase-mcp",
                    mcp_url="https://github.com/browserbase/mcp-server",
                    description="Browserbase MCP server for browser automation",
                    benefits=["Cloud-based browsers", "Better reliability", "No local setup"],
                    migration_effort="medium",
                    compatibility_score=0.85
                )
            ],

            # Design tools
            "figma-api": [
                MCPAlternative(
                    mcp_name="figma-mcp",
                    mcp_url="https://github.com/figma/mcp-server",
                    description="Figma MCP server for design workflows",
                    benefits=["Enhanced API access", "Better asset management", "Version control"],
                    migration_effort="low",
                    compatibility_score=0.9
                )
            ],

            # Ticketing
            "linear-sdk": [
                MCPAlternative(
                    mcp_name="linear-mcp",
                    mcp_url="https://github.com/linear/mcp-server",
                    description="Linear MCP server for issue tracking",
                    benefits=["Real-time updates", "Better search", "Enhanced workflows"],
                    migration_effort="low",
                    compatibility_score=0.95
                )
            ]
        }

    async def detect_integrations(self, repo_content: Dict[str, Any]) -> List[DetectedIntegration]:
        """
        Main method to detect integrations in repository content
        
        Args:
            repo_content: Repository content from analysis service
            
        Returns:
            List of detected integrations
        """
        logger.info("Starting integration detection analysis")
        
        integrations = []
        
        # Detect from package dependencies
        package_integrations = await self._detect_from_packages(repo_content)
        integrations.extend(package_integrations)
        
        # Detect from code patterns
        code_integrations = await self._detect_from_code_patterns(repo_content)
        integrations.extend(code_integrations)
        
        # Detect from environment variables and config files
        env_integrations = await self._detect_from_environment(repo_content)
        integrations.extend(env_integrations)
        
        # Merge and deduplicate integrations
        merged_integrations = self._merge_integrations(integrations)

        # Add MCP alternatives to each integration
        for integration in merged_integrations:
            # Get static alternatives
            static_alternatives = self.mcp_alternatives.get(integration.service_name, [])

            # Get dynamic alternatives from discovered servers
            dynamic_alternatives = await self._get_dynamic_mcp_alternatives(integration)

            # Combine and deduplicate alternatives
            all_alternatives = static_alternatives + dynamic_alternatives
            unique_alternatives = self._deduplicate_alternatives(all_alternatives)

            integration.mcp_alternatives = [alt.__dict__ for alt in unique_alternatives]

        logger.info(f"Detected {len(merged_integrations)} integrations")
        return merged_integrations

    async def _get_dynamic_mcp_alternatives(self, integration: DetectedIntegration) -> List[MCPAlternative]:
        """Get MCP alternatives from discovered servers database"""
        try:
            from app.database import get_db
            from app.models.analysis import MCPServer

            # Map integration types to MCP categories
            category_mapping = {
                "payment": "payment",
                "email": "communication",
                "sms": "communication",
                "database": "database",
                "cloud_storage": "cloud",
                "analytics": "monitoring",
                "ai_chat": "ai_chat",
                "development_tools": "development",
                "monitoring": "monitoring",
                "design_tools": "design",
                "ticketing": "ticketing",
                "scraping_search": "search"
            }

            category = category_mapping.get(integration.integration_type, "general")

            db = next(get_db())
            servers = db.query(MCPServer).filter(
                MCPServer.category == category,
                MCPServer.is_active == True,
                MCPServer.confidence_score >= 0.7
            ).order_by(MCPServer.confidence_score.desc()).limit(5).all()

            alternatives = []
            for server in servers:
                # Calculate migration effort based on service similarity
                migration_effort = self._calculate_migration_effort(integration, server)

                alternative = MCPAlternative(
                    mcp_name=server.name,
                    mcp_url=server.url,
                    description=server.description or f"{server.name} MCP server",
                    benefits=[
                        "Standardized MCP interface",
                        "Better error handling",
                        "Enhanced debugging",
                        "Unified tool ecosystem"
                    ],
                    migration_effort=migration_effort,
                    compatibility_score=server.confidence_score
                )
                alternatives.append(alternative)

            db.close()
            return alternatives

        except Exception as e:
            logger.error(f"Error getting dynamic MCP alternatives: {str(e)}")
            return []

    def _calculate_migration_effort(self, integration: DetectedIntegration, server) -> str:
        """Calculate migration effort based on integration and server characteristics"""
        # Check if service name matches server name
        if integration.service_name.lower() in server.name.lower():
            return "low"

        # Check if it's the same category
        category_mapping = {
            "payment": "payment",
            "email": "communication",
            "sms": "communication",
            "database": "database",
            "cloud_storage": "cloud"
        }

        if category_mapping.get(integration.integration_type) == server.category:
            return "medium"

        return "high"

    def _deduplicate_alternatives(self, alternatives: List[MCPAlternative]) -> List[MCPAlternative]:
        """Remove duplicate MCP alternatives based on URL"""
        seen_urls = set()
        unique_alternatives = []

        for alt in alternatives:
            if alt.mcp_url not in seen_urls:
                seen_urls.add(alt.mcp_url)
                unique_alternatives.append(alt)

        return unique_alternatives
    
    async def _detect_from_packages(self, repo_content: Dict[str, Any]) -> List[DetectedIntegration]:
        """Detect integrations from package dependencies"""
        integrations = []
        dependencies = repo_content.get("dependencies", [])
        
        for dep in dependencies:
            package_name = dep.get("name", "").lower()
            file_path = dep.get("file_path", "")
            
            for integration_type, patterns in self.integration_patterns.items():
                for pattern_package, config in patterns["packages"].items():
                    if pattern_package in package_name:
                        integration = DetectedIntegration(
                            integration_type=integration_type,
                            service_name=pattern_package,
                            detection_method="package_dependency",
                            confidence=config["confidence"],
                            file_locations=[file_path],
                            package_names=[package_name],
                            code_patterns=[],
                            env_variables=[],
                            migration_complexity=config["complexity"],
                            description=patterns["description"]
                        )
                        integrations.append(integration)
        
        return integrations
    
    async def _detect_from_code_patterns(self, repo_content: Dict[str, Any]) -> List[DetectedIntegration]:
        """Detect integrations from code patterns"""
        integrations = []
        code_samples = repo_content.get("code_samples", {})
        
        for file_path, content in code_samples.items():
            if not isinstance(content, str):
                continue
                
            for integration_type, patterns in self.integration_patterns.items():
                for pattern in patterns["code_patterns"]:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        # Determine service name from pattern
                        service_name = self._extract_service_name(pattern, matches[0])
                        
                        integration = DetectedIntegration(
                            integration_type=integration_type,
                            service_name=service_name,
                            detection_method="code_pattern",
                            confidence=0.8,  # Code patterns are fairly reliable
                            file_locations=[file_path],
                            package_names=[],
                            code_patterns=[pattern],
                            env_variables=[],
                            migration_complexity="medium",
                            description=patterns["description"]
                        )
                        integrations.append(integration)
        
        return integrations
    
    async def _detect_from_environment(self, repo_content: Dict[str, Any]) -> List[DetectedIntegration]:
        """Detect integrations from environment variables and config files"""
        integrations = []
        
        # Check environment files and config files
        env_files = [".env", ".env.example", "config.py", "settings.py", "docker-compose.yml"]
        code_samples = repo_content.get("code_samples", {})
        
        for file_path, content in code_samples.items():
            if not isinstance(content, str):
                continue
                
            # Check if this is an environment or config file
            if any(env_file in file_path.lower() for env_file in env_files):
                for integration_type, patterns in self.integration_patterns.items():
                    for env_pattern in patterns["env_patterns"]:
                        matches = re.findall(env_pattern, content, re.IGNORECASE)
                        if matches:
                            service_name = self._extract_service_from_env(env_pattern, matches[0])
                            
                            integration = DetectedIntegration(
                                integration_type=integration_type,
                                service_name=service_name,
                                detection_method="environment_variable",
                                confidence=0.7,  # Env vars are somewhat reliable
                                file_locations=[file_path],
                                package_names=[],
                                code_patterns=[],
                                env_variables=matches,
                                migration_complexity="low",
                                description=patterns["description"]
                            )
                            integrations.append(integration)
        
        return integrations
    
    def _extract_service_name(self, pattern: str, match: str) -> str:
        """Extract service name from code pattern match"""
        if "stripe" in pattern.lower():
            return "stripe"
        elif "paypal" in pattern.lower():
            return "paypal"
        elif "sendgrid" in pattern.lower():
            return "sendgrid"
        elif "twilio" in pattern.lower():
            return "twilio"
        elif "boto3" in pattern.lower():
            return "aws"
        else:
            return "unknown"
    
    def _extract_service_from_env(self, pattern: str, match: str) -> str:
        """Extract service name from environment variable pattern"""
        match_lower = match.lower()
        if "stripe" in match_lower:
            return "stripe"
        elif "paypal" in match_lower:
            return "paypal"
        elif "sendgrid" in match_lower:
            return "sendgrid"
        elif "twilio" in match_lower:
            return "twilio"
        elif "aws" in match_lower:
            return "aws"
        else:
            return match.split("_")[0].lower()
    
    def _merge_integrations(self, integrations: List[DetectedIntegration]) -> List[DetectedIntegration]:
        """Merge duplicate integrations and increase confidence"""
        merged = {}
        
        for integration in integrations:
            key = f"{integration.integration_type}_{integration.service_name}"
            
            if key in merged:
                # Merge with existing
                existing = merged[key]
                existing.confidence = min(1.0, existing.confidence + 0.1)  # Boost confidence
                existing.file_locations.extend(integration.file_locations)
                existing.package_names.extend(integration.package_names)
                existing.code_patterns.extend(integration.code_patterns)
                existing.env_variables.extend(integration.env_variables)
                
                # Use the most complex migration complexity
                if integration.migration_complexity == "high":
                    existing.migration_complexity = "high"
                elif integration.migration_complexity == "medium" and existing.migration_complexity == "low":
                    existing.migration_complexity = "medium"
            else:
                merged[key] = integration
        
        return list(merged.values())
