"""
Intelligent Analysis Service using AI (OpenAI/Claude) for comprehensive repository business logic analysis
"""
import json
import logging
import asyncio
import re
from typing import Dict, List, Any, Optional
from openai import Async<PERSON>penAI
import anthropic
from ..config import settings
from .github_service import GitHubService

logger = logging.getLogger(__name__)


class IntelligentAnalysisService:
    """Service for AI-powered comprehensive repository analysis"""
    
    def __init__(self):
        # Initialize AI clients - prefer <PERSON> if available, fallback to OpenAI
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.anthropic_api_key) if settings.anthropic_api_key else None
        self.openai_client = AsyncOpenAI(api_key=settings.openai_api_key) if settings.openai_api_key else None
        self.github_service = GitHubService()
        self.max_files_to_analyze = 50  # Limit for API cost control
        self.max_file_size = 100000  # 100KB per file limit
        
        # Determine which AI service to use
        self.use_claude = bool(settings.anthropic_api_key)
        logger.info(f"Initialized AI service: {'<PERSON>' if self.use_claude else 'OpenAI' if self.openai_client else 'None'}")
    
    async def _call_ai_service(self, prompt: str, max_tokens: int = 4000) -> str:
        """Unified method to call either Claude or OpenAI with proper error handling"""
        try:
            if self.use_claude and self.anthropic_client:
                response = await self.anthropic_client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=max_tokens,
                    messages=[{"role": "user", "content": prompt}]
                )
                return response.content[0].text
            elif self.openai_client:
                response = await self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.3,
                    max_tokens=max_tokens
                )
                return response.choices[0].message.content
            else:
                raise ValueError("No AI service available (neither Claude nor OpenAI API key configured)")

        except Exception as e:
            error_msg = str(e).lower()

            # Check for specific error types
            if "insufficient_quota" in error_msg or "quota" in error_msg or "billing" in error_msg:
                raise Exception("AI_QUOTA_EXCEEDED: Your AI service quota has been exceeded. Please check your billing and usage limits.")
            elif "rate_limit" in error_msg or "rate limit" in error_msg:
                raise Exception("AI_RATE_LIMITED: AI service rate limit exceeded. Please try again in a few minutes.")
            elif "invalid_api_key" in error_msg or "unauthorized" in error_msg:
                raise Exception("AI_AUTH_ERROR: Invalid AI API key. Please check your configuration.")
            elif "model_not_found" in error_msg or "model" in error_msg:
                raise Exception("AI_MODEL_ERROR: AI model not available or not found.")
            else:
                raise Exception(f"AI_SERVICE_ERROR: {str(e)}")

    def _extract_json_from_response(self, content: str) -> Dict[str, Any]:
        """Robust JSON extraction from AI response that may contain extra text"""
        try:
            # First, try direct JSON parsing
            return json.loads(content.strip())
        except json.JSONDecodeError:
            pass

        # Try extracting from code blocks
        if '```json' in content:
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1).strip())
                except json.JSONDecodeError:
                    pass

        # Try extracting from any code block
        if '```' in content:
            json_match = re.search(r'```[a-zA-Z]*\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1).strip())
                except json.JSONDecodeError:
                    pass

        # Try finding JSON object boundaries
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(0))
            except json.JSONDecodeError:
                pass

        # If all else fails, raise an error
        raise json.JSONDecodeError(f"Could not extract valid JSON from response: {content[:200]}...", content, 0)

    async def _analyze_everything_comprehensive(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Single comprehensive analysis for Claude (large context window)"""
        
        # Create comprehensive prompt combining all analysis types
        prompt = f"""
Analyze this repository comprehensively for MCP (Model Context Protocol) server development potential.

Repository: {repo_content['repository_info']['name']}
Description: {repo_content['repository_info'].get('description', 'No description')}
Language: {repo_content['repository_info'].get('language', 'Unknown')}
Topics: {repo_content['repository_info'].get('topics', [])}

Repository Structure:
{json.dumps(repo_content.get('repository_tree', []), indent=2)[:5000]}

API Endpoints Detected:
{json.dumps(repo_content.get('api_endpoints', []), indent=2)[:2000]}

Code Files (key files):
{json.dumps({k: v for k, v in list(repo_content.get('code_samples', {}).items())[:10]}, indent=2)[:10000]}

Configuration Files:
{json.dumps(repo_content.get('config_files', {}), indent=2)[:3000]}

ANALYSIS INSTRUCTIONS:
1. First, understand the CORE BUSINESS LOGIC by analyzing the main code files
2. Identify the PRIMARY DOMAIN and what this repository actually does
3. Analyze the API ENDPOINTS to understand external interfaces
4. Examine CONFIGURATION FILES to understand dependencies and setup
5. Only then suggest MCP tools that would genuinely help AI assistants interact with this specific repository's functionality

Please provide a comprehensive analysis in this exact JSON format:

{{
    "business_logic": {{
        "primary_domain": "domain (e.g., 'document processing', 'web scraping', 'data analysis')",
        "business_purpose": "clear description of what this repository does",
        "core_operations": ["list", "of", "main", "business", "operations"],
        "business_entities": ["main", "data", "entities"],
        "business_rules": ["key", "business", "rules"],
        "target_users": ["types", "of", "users"],
        "use_cases": [
            {{
                "scenario": "business scenario",
                "user_story": "as a user, I want to...",
                "business_outcome": "expected result"
            }}
        ],
        "value_proposition": "core business value"
    }},
    "workflows": {{
        "data_flows": ["step1 -> step2 -> step3"],
        "business_processes": ["main", "business", "processes"],
        "integration_patterns": ["how", "it", "integrates"],
        "automation_opportunities": ["things", "that", "could", "be", "automated"]
    }},
    "api_capabilities": {{
        "existing_apis": [
            {{
                "endpoint": "/api/endpoint",
                "method": "GET/POST",
                "purpose": "what it does"
            }}
        ],
        "api_patterns": ["REST", "GraphQL", "etc"],
        "authentication": {{
            "methods": ["JWT", "OAuth", "etc"],
            "description": "auth approach"
        }},
        "external_integrations": ["external", "services", "used"],
        "potential_new_apis": [
            {{
                "endpoint": "/api/new-endpoint",
                "purpose": "potential new functionality",
                "business_value": "why it would be useful"
            }}
        ]
    }},
    "integration_opportunities": {{
        "current_integrations": ["existing", "integrations"],
        "data_management": {{
            "databases": ["database", "types"],
            "data_formats": ["JSON", "XML", "etc"],
            "storage_patterns": ["how", "data", "is", "stored"]
        }},
        "external_services": ["third", "party", "services"],
        "integration_opportunities": ["new", "integration", "possibilities"],
        "data_transformations": ["data", "transformation", "needs"]
    }},
    "mcp_suggestions": {{
        "categories": {{
            "CORE_BUSINESS_TOOLS": [
                {{
                    "name": "tool_name",
                    "description": "what it does",
                    "business_value": "business benefit",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["when", "to", "use", "it"]
                }},
                {{
                    "name": "another_core_tool",
                    "description": "another core business capability",
                    "business_value": "additional business value",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["more", "use", "cases"]
                }}
            ],
            "WORKFLOW_AUTOMATION": [
                {{
                    "name": "automation_tool",
                    "description": "workflow automation capability",
                    "business_value": "efficiency gain",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["automation", "scenarios"]
                }},
                {{
                    "name": "process_optimizer",
                    "description": "process optimization and orchestration",
                    "business_value": "streamlined workflows",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["process", "automation"]
                }}
            ],
            "DATA_INTEGRATION": [
                {{
                    "name": "data_tool",
                    "description": "data integration capability",
                    "business_value": "data value",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["data", "scenarios"]
                }},
                {{
                    "name": "sync_manager",
                    "description": "data synchronization and transformation",
                    "business_value": "unified data access",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["data", "sync", "transform"]
                }}
            ],
            "ANALYTICS_INTELLIGENCE": [
                {{
                    "name": "analytics_tool",
                    "description": "business intelligence and reporting",
                    "business_value": "data-driven insights",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["analytics", "reporting", "insights"]
                }},
                {{
                    "name": "metrics_dashboard",
                    "description": "performance metrics and KPI tracking",
                    "business_value": "operational visibility",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["metrics", "monitoring", "kpis"]
                }}
            ],
            "COMPLIANCE_SECURITY": [
                {{
                    "name": "security_tool",
                    "description": "security and compliance automation",
                    "business_value": "risk mitigation",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["security", "compliance", "audit"]
                }},
                {{
                    "name": "audit_tracker",
                    "description": "compliance monitoring and audit trails",
                    "business_value": "regulatory compliance",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["audit", "compliance", "tracking"]
                }}
            ],
            "OPTIMIZATION_PERFORMANCE": [
                {{
                    "name": "performance_tool",
                    "description": "performance optimization and monitoring",
                    "business_value": "improved efficiency",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["performance", "optimization", "monitoring"]
                }},
                {{
                    "name": "cost_optimizer",
                    "description": "cost analysis and optimization",
                    "business_value": "reduced operational costs",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["cost", "optimization", "analysis"]
                }}
            ],
            "USER_EXPERIENCE": [
                {{
                    "name": "ux_tool",
                    "description": "user experience and interface tools",
                    "business_value": "improved user satisfaction",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["user", "experience", "interface"]
                }},
                {{
                    "name": "interaction_helper",
                    "description": "user interaction and support tools",
                    "business_value": "enhanced user engagement",
                    "implementation_effort": "low/medium/high",
                    "use_cases": ["user", "support", "interaction"]
                }}
            ]
        }},
        "prioritized_recommendations": [
            {{
                "tool_name": "highest_priority_tool",
                "priority": "high",
                "reasoning": "why this is most important",
                "estimated_hours": 8
            }}
        ],
        "implementation_roadmap": {{
            "phase_1_quick_wins": ["tools", "that", "are", "easy", "to", "implement"],
            "phase_2_core_features": ["main", "functionality", "tools"],
            "phase_3_advanced": ["complex", "advanced", "tools"]
        }},
        "total_tools_suggested": 14,
        "estimated_total_effort_hours": 120
    }}
}}

CRITICAL REQUIREMENTS:
1. You MUST generate tools for ALL 7 categories: CORE_BUSINESS_TOOLS, WORKFLOW_AUTOMATION, DATA_INTEGRATION, ANALYTICS_INTELLIGENCE, COMPLIANCE_SECURITY, OPTIMIZATION_PERFORMANCE, USER_EXPERIENCE
2. Generate 2-3 high-value MCP tools for EACH category (minimum 14 tools total)
3. Each tool must be repository-specific and practical
4. Each tool must have clear business value and specific use cases
5. Do not skip any categories - all 7 are required
6. Tools should be tailored to this specific repository's functionality and domain
"""
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=8000)
            content = content.strip()
            
            # Extract JSON from response using robust method
            result = self._extract_json_from_response(content)
            logger.info("Comprehensive analysis completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {str(e)}")
            # Return empty structure if analysis fails
            return {
                "business_logic": {
                    "primary_domain": "unknown",
                    "business_purpose": "Analysis failed",
                    "core_operations": [],
                    "business_entities": [],
                    "business_rules": [],
                    "target_users": [],
                    "use_cases": [],
                    "value_proposition": "Could not determine"
                },
                "workflows": {
                    "data_flows": [],
                    "business_processes": [],
                    "integration_patterns": [],
                    "automation_opportunities": []
                },
                "api_capabilities": {
                    "existing_apis": [],
                    "api_patterns": [],
                    "authentication": {"methods": [], "description": ""},
                    "external_integrations": [],
                    "potential_new_apis": []
                },
                "integration_opportunities": {
                    "current_integrations": [],
                    "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                    "external_services": [],
                    "integration_opportunities": [],
                    "data_transformations": []
                },
                "mcp_suggestions": {
                    "categories": {
                        "CORE_BUSINESS_TOOLS": [],
                        "WORKFLOW_AUTOMATION": [],
                        "DATA_INTEGRATION": [],
                        "ANALYTICS_INTELLIGENCE": [],
                        "COMPLIANCE_SECURITY": [],
                        "OPTIMIZATION_PERFORMANCE": [],
                        "USER_EXPERIENCE": []
                    },
                    "prioritized_recommendations": [],
                    "implementation_roadmap": {"phase_1_quick_wins": [], "phase_2_core_features": [], "phase_3_advanced": []},
                    "total_tools_suggested": 0,
                    "estimated_total_effort_hours": 0
                }
            }
        
    async def analyze_repository_comprehensively(
        self, 
        repo_owner: str,
        repo_name: str,
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive business logic analysis using OpenAI
        """
        try:
            logger.info(f"Starting comprehensive analysis for {repo_owner}/{repo_name}")
            
            # 1. Gather comprehensive repository content
            repo_content = await self._gather_repository_content(
                repo_owner, repo_name, github_token, repo_info
            )
            
            # Add small delay to make progress visible
            await asyncio.sleep(1)
            
            # 2-6. Perform analysis (single call for Claude, multiple for OpenAI)
            if self.use_claude:
                # Use comprehensive single-call analysis for Claude
                comprehensive_results = await self._analyze_everything_comprehensive(repo_content)
                # Add delay to show AI processing time
                await asyncio.sleep(2)

                # Handle both direct and nested comprehensive analysis structure
                comp_analysis = comprehensive_results.get("comprehensive_analysis", comprehensive_results)
                business_analysis = comp_analysis.get("business_logic", {})
                workflow_analysis = comp_analysis.get("workflows", {})
                api_analysis = comp_analysis.get("api_capabilities", {})
                integration_analysis = comp_analysis.get("integration_opportunities", {})
                mcp_suggestions = comprehensive_results.get("mcp_suggestions", {})
            else:
                # For OpenAI, use separate calls with delays (rate limiting)
                business_analysis = await self._analyze_business_logic(repo_content)
                await asyncio.sleep(22)  # Rate limit: 3 requests/minute = 20s between calls + buffer
                
                workflow_analysis = await self._analyze_workflows(repo_content)
                await asyncio.sleep(22)
                
                api_analysis = await self._analyze_api_capabilities(repo_content)
                await asyncio.sleep(22)
                
                integration_analysis = await self._analyze_integration_points(repo_content)
                await asyncio.sleep(22)
                
                mcp_suggestions = await self._generate_mcp_suggestions({
                    "repository_info": repo_content["repository_info"],
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "apis": api_analysis,
                    "integrations": integration_analysis,
                    "code_samples": repo_content["code_samples"]
                })
            
            # 7. Calculate confidence and complexity scores
            confidence_score = self._calculate_analysis_confidence(mcp_suggestions)
            complexity_assessment = self._assess_implementation_complexity(mcp_suggestions)
            
            return {
                "comprehensive_analysis": {
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "api_capabilities": api_analysis,
                    "integration_opportunities": integration_analysis
                },
                "mcp_suggestions": mcp_suggestions,
                "confidence_score": confidence_score,
                "implementation_complexity": complexity_assessment,
                "analysis_metadata": {
                    "files_analyzed": repo_content.get("files_count", 0),
                    "analysis_timestamp": repo_content.get("analysis_timestamp"),
                    "repository_size": repo_content.get("repository_size", 0)
                },
                "repository_tree": repo_content.get("repository_tree", []),
                "repository_info": repo_content.get("repository_info", {})
            }
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed for {repo_owner}/{repo_name}: {str(e)}")
            raise Exception(f"AI-powered analysis failed: {str(e)}")
    
    async def _gather_repository_content(
        self, 
        repo_owner: str, 
        repo_name: str, 
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Gather comprehensive repository content for analysis"""
        
        # Get basic repository info if not provided
        if not repo_info:
            repo_info = self.github_service.get_repository_info_sync(
                repo_owner, repo_name, github_token
            )
        
        # Get repository tree and important files
        repo_tree = self.github_service.get_repository_tree_sync(
            repo_owner, repo_name, github_token, max_depth=3
        )
        
        # Identify key files to analyze
        important_files = self._identify_important_files(repo_tree)

        # Get file contents for analysis
        code_samples = {}
        api_endpoints = []
        config_files = {}
        files_analyzed = 0

        for file_path in important_files[:self.max_files_to_analyze]:
            try:
                file_content = self.github_service.get_file_content_sync(
                    repo_owner, repo_name, file_path, github_token
                )

                if file_content and len(file_content) <= self.max_file_size:
                    code_samples[file_path] = file_content
                    files_analyzed += 1

                    # Extract API endpoints from code
                    if any(ext in file_path.lower() for ext in ['.py', '.js', '.ts', '.go', '.java']):
                        endpoints = self._extract_api_endpoints_from_code(file_content, file_path)
                        api_endpoints.extend(endpoints)

                    # Collect configuration files
                    if any(config in file_path.lower() for config in ['config', 'settings', '.env', 'package.json', 'requirements.txt', 'cargo.toml']):
                        config_files[file_path] = file_content[:2000]  # Limit config file size
                    
            except Exception as e:
                logger.warning(f"Failed to get content for {file_path}: {str(e)}")
                continue
        
        # Get nested tree structure for frontend display
        nested_tree = self.github_service.get_repository_tree_nested(
            repo_owner, repo_name, github_token, max_depth=3
        )
        
        return {
            "repository_info": repo_info,
            "repository_tree": nested_tree,
            "code_samples": code_samples,
            "api_endpoints": api_endpoints,
            "config_files": config_files,
            "files_count": files_analyzed,
            "repository_size": repo_info.get("size", 0),
            "analysis_timestamp": repo_info.get("updated_at")
        }
    
    def _identify_important_files(self, repo_tree: List[Dict]) -> List[str]:
        """Identify important files for business logic analysis"""
        
        important_patterns = [
            # Configuration and setup files
            "README.md", "setup.py", "pyproject.toml", "package.json", 
            "Cargo.toml", "go.mod", "pom.xml", "build.gradle",
            
            # Main application files
            "main.py", "app.py", "index.js", "server.js", "main.go",
            "__init__.py", "cli.py", "command.py",
            
            # API and service files
            "api/", "routes/", "handlers/", "controllers/", "services/",
            "endpoints/", "views/", "models/",
            
            # Documentation
            "docs/", "documentation/", "examples/", "CHANGELOG",
            
            # Configuration
            "config/", "settings/", "env", ".env.example"
        ]
        
        important_files = []
        
        for item in repo_tree:
            file_path = item.get("path", "")
            file_name = file_path.split("/")[-1]
            
            # Check if file matches important patterns
            for pattern in important_patterns:
                if (pattern in file_path.lower() or 
                    pattern in file_name.lower() or
                    file_path.startswith(pattern) or
                    file_name == pattern):
                    important_files.append(file_path)
                    break
        
        # Sort by importance (main files first, then config, then others)
        def file_importance(file_path):
            main_files = ["main.py", "app.py", "index.js", "server.js", "README.md"]
            if any(main in file_path.lower() for main in main_files):
                return 0
            elif "api" in file_path.lower() or "route" in file_path.lower():
                return 1
            elif "config" in file_path.lower() or "setup" in file_path.lower():
                return 2
            else:
                return 3
        
        important_files.sort(key=file_importance)
        return important_files

    def _extract_api_endpoints_from_code(self, file_content: str, file_path: str) -> List[Dict[str, Any]]:
        """Extract API endpoints from code content"""
        endpoints = []

        # Common API endpoint patterns
        patterns = [
            # FastAPI/Flask Python
            r'@app\.(get|post|put|delete|patch)\(["\']([^"\']+)["\']',
            r'@router\.(get|post|put|delete|patch)\(["\']([^"\']+)["\']',
            r'app\.route\(["\']([^"\']+)["\'].*methods=\[([^\]]+)\]',

            # Express.js
            r'app\.(get|post|put|delete|patch)\(["\']([^"\']+)["\']',
            r'router\.(get|post|put|delete|patch)\(["\']([^"\']+)["\']',

            # Spring Boot Java
            r'@(Get|Post|Put|Delete|Patch)Mapping\(["\']([^"\']+)["\']',
            r'@RequestMapping\([^)]*value\s*=\s*["\']([^"\']+)["\']',

            # Go Gin/Mux
            r'r\.(GET|POST|PUT|DELETE|PATCH)\(["\']([^"\']+)["\']',
            r'HandleFunc\(["\']([^"\']+)["\']'
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, file_content, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) >= 2:
                    method = match.group(1).upper() if match.group(1) else 'GET'
                    path = match.group(2) if len(match.groups()) >= 2 else match.group(1)

                    endpoints.append({
                        "method": method,
                        "path": path,
                        "file": file_path,
                        "line_context": self._get_line_context(file_content, match.start())
                    })

        return endpoints

    def _get_line_context(self, content: str, position: int) -> str:
        """Get surrounding lines for context"""
        lines = content[:position].split('\n')
        line_num = len(lines)
        all_lines = content.split('\n')

        start = max(0, line_num - 2)
        end = min(len(all_lines), line_num + 3)

        return '\n'.join(all_lines[start:end])
    
    async def _analyze_business_logic(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze repository's core business logic"""
        
        prompt = f"""
        Analyze this repository's core business logic and purpose:

        Repository Info:
        - Name: {repo_content['repository_info'].get('name')}
        - Description: {repo_content['repository_info'].get('description')}
        - Language: {repo_content['repository_info'].get('language')}
        - Topics: {repo_content['repository_info'].get('topics', [])}

        Key Files Content:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:5]}, indent=2)}

        Please analyze and identify:
        1. Primary business domain (e.g., document processing, data analysis, web scraping, etc.)
        2. Core business operations and their purposes
        3. Key business entities and data models
        4. Business rules and logic patterns
        5. Value proposition - what business problem does this solve?
        6. Target users and use cases
        7. Business processes and workflows

        Return as JSON with this structure:
        {{
            "primary_domain": "string",
            "business_purpose": "detailed description",
            "core_operations": [
                {{
                    "name": "operation_name",
                    "purpose": "what it does for business",
                    "business_value": "why it matters",
                    "complexity": "low|medium|high"
                }}
            ],
            "business_entities": ["entity1", "entity2"],
            "business_rules": ["rule1", "rule2"],
            "target_users": ["user_type1", "user_type2"],
            "use_cases": [
                {{
                    "scenario": "business scenario",
                    "user_story": "as a user, I want to...",
                    "business_outcome": "expected result"
                }}
            ],
            "value_proposition": "core business value"
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Business logic analysis failed: {str(e)}")
            return {
                "primary_domain": "unknown",
                "business_purpose": "Analysis failed",
                "core_operations": [],
                "business_entities": [],
                "business_rules": [],
                "target_users": [],
                "use_cases": [],
                "value_proposition": "Could not determine"
            }
    
    async def _analyze_workflows(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze workflow patterns and business processes"""
        
        prompt = f"""
        Analyze the workflow patterns and business processes in this repository:

        Code Samples:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:8]}, indent=2)}

        Identify:
        1. Data flow patterns (input → processing → output)
        2. Business process workflows
        3. Integration points with external systems
        4. Automation opportunities
        5. Decision points and business rules
        6. Error handling and recovery processes

        Return as JSON:
        {{
            "data_flows": [
                {{
                    "name": "flow_name",
                    "description": "what the flow does",
                    "inputs": ["input1", "input2"],
                    "processing_steps": ["step1", "step2"],
                    "outputs": ["output1", "output2"],
                    "business_impact": "why this flow matters"
                }}
            ],
            "business_processes": [
                {{
                    "process_name": "name",
                    "description": "what it accomplishes",
                    "steps": ["step1", "step2"],
                    "automation_level": "manual|semi-automated|automated",
                    "improvement_opportunities": ["opportunity1"]
                }}
            ],
            "integration_patterns": [
                {{
                    "integration_type": "API|Database|File|etc",
                    "description": "what it integrates with",
                    "data_exchanged": ["data_type1"],
                    "frequency": "real-time|batch|on-demand"
                }}
            ],
            "automation_opportunities": [
                {{
                    "opportunity": "what could be automated",
                    "current_state": "how it works now",
                    "potential_benefit": "business value of automation",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Workflow analysis failed: {str(e)}")
            return {
                "data_flows": [],
                "business_processes": [],
                "integration_patterns": [],
                "automation_opportunities": []
            }
    
    async def _analyze_api_capabilities(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze API patterns and capabilities"""
        
        prompt = f"""
        Analyze the API capabilities and patterns in this repository:

        Code Samples:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:10]}, indent=2)}

        Identify:
        1. Existing API endpoints and their purposes
        2. API design patterns used
        3. Authentication and authorization methods
        4. Data formats and schemas
        5. Rate limiting and performance considerations
        6. External API integrations
        7. Potential new API endpoints that could be created

        Return as JSON:
        {{
            "existing_apis": [
                {{
                    "endpoint": "/api/endpoint",
                    "method": "GET|POST|PUT|DELETE",
                    "purpose": "what it does",
                    "input_schema": {{"param": "type"}},
                    "output_schema": {{"result": "type"}},
                    "business_function": "business purpose"
                }}
            ],
            "api_patterns": [
                {{
                    "pattern_name": "REST|GraphQL|RPC|etc",
                    "usage": "how it's used",
                    "benefits": "advantages for business"
                }}
            ],
            "authentication": {{
                "methods": ["API_KEY", "OAuth", "JWT"],
                "description": "how auth works"
            }},
            "external_integrations": [
                {{
                    "service": "external service name",
                    "purpose": "why it's integrated",
                    "api_type": "REST|GraphQL|etc",
                    "data_exchanged": ["data_type1"]
                }}
            ],
            "potential_new_apis": [
                {{
                    "proposed_endpoint": "/api/new-endpoint",
                    "purpose": "business need it would serve",
                    "value_proposition": "why create this API",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"API analysis failed: {str(e)}")
            return {
                "existing_apis": [],
                "api_patterns": [],
                "authentication": {"methods": [], "description": ""},
                "external_integrations": [],
                "potential_new_apis": []
            }
    
    async def _analyze_integration_points(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze integration opportunities and system connections"""
        
        prompt = f"""
        Analyze integration opportunities and system connection points:

        Repository: {repo_content['repository_info'].get('name')}
        Code Analysis:
        {json.dumps({k: v for k, v in list(repo_content['code_samples'].items())[:8]}, indent=2)}

        Identify:
        1. Current system integrations
        2. Database connections and data management
        3. File system operations
        4. Network communications
        5. Third-party service integrations
        6. Potential new integration opportunities
        7. Data transformation and ETL processes

        Return as JSON:
        {{
            "current_integrations": [
                {{
                    "integration_name": "name",
                    "type": "Database|API|File|Service",
                    "purpose": "business reason",
                    "data_flow": "bidirectional|input|output",
                    "criticality": "high|medium|low"
                }}
            ],
            "data_management": {{
                "databases": ["db_type1", "db_type2"],
                "data_formats": ["JSON", "CSV", "XML"],
                "storage_patterns": ["pattern1", "pattern2"]
            }},
            "external_services": [
                {{
                    "service": "service_name",
                    "purpose": "why it's used",
                    "integration_method": "API|SDK|Direct",
                    "business_dependency": "high|medium|low"
                }}
            ],
            "integration_opportunities": [
                {{
                    "opportunity": "potential integration",
                    "business_value": "what business value it would provide",
                    "technical_approach": "how to implement",
                    "effort_required": "low|medium|high",
                    "roi_potential": "high|medium|low"
                }}
            ],
            "data_transformations": [
                {{
                    "transformation": "what data is transformed",
                    "from_format": "source format",
                    "to_format": "target format",
                    "business_purpose": "why transformation is needed"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Integration analysis failed: {str(e)}")
            return {
                "current_integrations": [],
                "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                "external_services": [],
                "integration_opportunities": [],
                "data_transformations": []
            }
    
    async def _generate_mcp_suggestions(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate categorized MCP tool suggestions based on comprehensive analysis"""
        
        prompt = f"""
        Based on this comprehensive repository analysis, suggest specific MCP tools that would provide genuine business value:

        Analysis Data:
        {json.dumps(analysis_data, indent=2)}

        Generate MCP tool suggestions in these categories:
        1. CORE_BUSINESS_TOOLS: Direct business logic operations
        2. WORKFLOW_AUTOMATION: Process automation and orchestration  
        3. DATA_INTEGRATION: Data transformation and system integration
        4. ANALYTICS_INTELLIGENCE: Business intelligence and reporting
        5. COMPLIANCE_SECURITY: Compliance, audit, and security tools
        6. OPTIMIZATION_PERFORMANCE: Performance and cost optimization
        7. USER_EXPERIENCE: End-user interaction and interface tools

        For each tool, provide:
        - tool_name: Clear, descriptive name (snake_case)
        - category: Which category it belongs to
        - business_value: Why this tool would be valuable for AI assistants
        - complexity_level: low/medium/high implementation complexity
        - input_schema: Expected input parameters with types
        - output_schema: Expected output structure with types
        - implementation_hints: Key technical considerations for building this
        - use_cases: Specific scenarios where AI assistants would use this
        - dependencies: What from the original repo this tool would depend on
        - estimated_effort_hours: Realistic development time estimate

        Focus on tools that:
        - Solve real business problems identified in the analysis
        - Would be frequently used by AI assistants
        - Leverage the unique capabilities of this repository
        - Provide clear, measurable business value

        Return as valid JSON:
        {{
            "categories": {{
                "CORE_BUSINESS_TOOLS": [tool_objects],
                "WORKFLOW_AUTOMATION": [tool_objects],
                "DATA_INTEGRATION": [tool_objects],
                "ANALYTICS_INTELLIGENCE": [tool_objects],
                "COMPLIANCE_SECURITY": [tool_objects],
                "OPTIMIZATION_PERFORMANCE": [tool_objects],
                "USER_EXPERIENCE": [tool_objects]
            }},
            "prioritized_recommendations": [
                {{
                    "tool_name": "highest_value_tool",
                    "priority_score": 95,
                    "reasoning": "why this is highest priority"
                }}
            ],
            "implementation_roadmap": {{
                "phase_1_quick_wins": [
                    {{
                        "tool_name": "easy_tool",
                        "effort_hours": 8,
                        "business_impact": "high|medium|low"
                    }}
                ],
                "phase_2_core_features": [
                    {{
                        "tool_name": "medium_tool", 
                        "effort_hours": 24,
                        "business_impact": "high|medium|low"
                    }}
                ],
                "phase_3_advanced": [
                    {{
                        "tool_name": "complex_tool",
                        "effort_hours": 40,
                        "business_impact": "high|medium|low"
                    }}
                ]
            }},
            "total_tools_suggested": 0,
            "estimated_total_effort_hours": 0
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=4000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            # Extract JSON from response using robust method
            suggestions = self._extract_json_from_response(content)
            
            # Calculate totals
            total_tools = sum(len(tools) for tools in suggestions["categories"].values())
            total_effort = self._calculate_total_effort(suggestions)
            
            suggestions["total_tools_suggested"] = total_tools
            suggestions["estimated_total_effort_hours"] = total_effort
            
            return suggestions
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"MCP suggestions generation failed: {error_msg}")

            # Check for specific AI service errors
            if error_msg.startswith("AI_QUOTA_EXCEEDED"):
                raise Exception("AI service quota exceeded. Please check your billing and try again later.")
            elif error_msg.startswith("AI_RATE_LIMITED"):
                raise Exception("AI service rate limited. Please wait a few minutes and try again.")
            elif error_msg.startswith("AI_AUTH_ERROR"):
                raise Exception("AI service authentication failed. Please check your API key configuration.")
            elif error_msg.startswith("AI_MODEL_ERROR"):
                raise Exception("AI model not available. Please try again later.")
            else:
                raise Exception(f"Failed to generate MCP suggestions: {error_msg}")
    
    def _calculate_total_effort(self, suggestions: Dict[str, Any]) -> int:
        """Calculate total effort hours from all suggested tools"""
        total_hours = 0
        
        for phase_tools in suggestions.get("implementation_roadmap", {}).values():
            for tool in phase_tools:
                total_hours += tool.get("effort_hours", 0)
                
        return total_hours
    
    def _calculate_analysis_confidence(self, mcp_suggestions: Dict[str, Any]) -> float:
        """Calculate confidence score for the analysis"""
        
        # Base confidence on number of tools suggested and roadmap completeness
        total_tools = mcp_suggestions.get("total_tools_suggested", 0)
        has_roadmap = bool(mcp_suggestions.get("implementation_roadmap", {}).get("phase_1_quick_wins"))
        has_priorities = bool(mcp_suggestions.get("prioritized_recommendations"))
        
        confidence = 0.0
        
        if total_tools > 0:
            confidence += min(total_tools * 0.05, 0.4)  # Up to 40% for having tools
        
        if has_roadmap:
            confidence += 0.3  # 30% for having implementation roadmap
            
        if has_priorities:
            confidence += 0.3  # 30% for having prioritized recommendations
            
        return min(confidence, 1.0)
    
    def _assess_implementation_complexity(self, mcp_suggestions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall implementation complexity"""
        
        roadmap = mcp_suggestions.get("implementation_roadmap", {})
        total_effort = mcp_suggestions.get("estimated_total_effort_hours", 0)
        
        # Determine complexity level
        if total_effort <= 40:
            complexity_level = "low"
        elif total_effort <= 120:
            complexity_level = "medium"
        else:
            complexity_level = "high"
        
        return {
            "overall_complexity": complexity_level,
            "total_estimated_hours": total_effort,
            "quick_wins_available": len(roadmap.get("phase_1_quick_wins", [])),
            "advanced_features_count": len(roadmap.get("phase_3_advanced", [])),
            "recommendation": self._get_complexity_recommendation(complexity_level, total_effort)
        }
    
    def _get_complexity_recommendation(self, complexity_level: str, total_effort: int) -> str:
        """Get recommendation based on complexity assessment"""
        
        if complexity_level == "low":
            return f"Great candidate for MCP server development. Estimated {total_effort} hours for full implementation."
        elif complexity_level == "medium":
            return f"Good MCP server candidate. Consider phased implementation over {total_effort} hours."
        else:
            return f"Complex but valuable MCP server. Recommend starting with quick wins, full implementation ~{total_effort} hours."