"""
Intelligent Analysis Service using AI (OpenAI/Claude) for comprehensive repository business logic analysis
"""
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
from openai import Async<PERSON>penAI
import anthropic
from ..config import settings
from .github_service import GitHubService

logger = logging.getLogger(__name__)


class IntelligentAnalysisService:
    """Service for AI-powered comprehensive repository analysis"""
    
    def __init__(self):
        # Initialize AI clients - prefer <PERSON> if available, fallback to OpenAI
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.anthropic_api_key) if settings.anthropic_api_key else None
        self.openai_client = AsyncOpenAI(api_key=settings.openai_api_key) if settings.openai_api_key else None
        self.github_service = GitHubService()
        self.max_files_to_analyze = 50  # Limit for API cost control
        self.max_file_size = 100000  # 100KB per file limit
        
        # Determine which AI service to use
        self.use_claude = bool(settings.anthropic_api_key)
        logger.info(f"Initialized AI service: {'Claude' if self.use_claude else 'OpenAI' if self.openai_client else 'None'}")
    
    async def _call_ai_service(self, prompt: str, max_tokens: int = 4000) -> str:
        """Unified method to call either Claude or OpenAI with automatic fallback"""

        # Try Claude first if available
        if self.use_claude and self.anthropic_client:
            try:
                response = await self.anthropic_client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=max_tokens,
                    messages=[{"role": "user", "content": prompt}]
                )
                return response.content[0].text
            except Exception as claude_error:
                logger.warning(f"Claude API failed: {str(claude_error)}")
                # If Claude fails and we have OpenAI, try OpenAI as fallback
                if self.openai_client:
                    logger.info("Falling back to OpenAI API")
                    try:
                        response = await self.openai_client.chat.completions.create(
                            model="gpt-4o-mini",
                            messages=[{"role": "user", "content": prompt}],
                            temperature=0.3,
                            max_tokens=max_tokens
                        )
                        return response.choices[0].message.content
                    except Exception as openai_error:
                        logger.error(f"OpenAI API also failed: {str(openai_error)}")
                        raise ValueError(f"Both AI services failed. Claude: {str(claude_error)}, OpenAI: {str(openai_error)}")
                else:
                    raise ValueError(f"Claude API failed and no OpenAI fallback available: {str(claude_error)}")

        # If Claude is not available, try OpenAI directly
        elif self.openai_client:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        else:
            raise ValueError("No AI service available (neither Claude nor OpenAI API key configured)")

    def _clean_json_response(self, content: str) -> str:
        """Clean common JSON formatting issues in AI responses"""
        import re

        # Remove trailing commas before closing brackets/braces
        content = re.sub(r',(\s*[}\]])', r'\1', content)

        # Fix common quote issues
        content = content.replace('"', '"').replace('"', '"')
        content = content.replace(''', "'").replace(''', "'")

        # Remove any text before the first { or after the last }
        start = content.find('{')
        end = content.rfind('}')
        if start != -1 and end != -1 and end > start:
            content = content[start:end+1]

        # Fix common escape sequence issues
        content = content.replace('\\"', '"')

        return content

    async def _analyze_everything_comprehensive(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Single comprehensive analysis for Claude (large context window)"""

        # Simplified and focused analysis prompt
        repo_name = repo_content['repository_info']['name']
        repo_desc = repo_content['repository_info'].get('description', 'No description')
        repo_lang = repo_content['repository_info'].get('language', 'Unknown')

        # Safely get code samples
        code_samples = repo_content.get('code_samples', {})
        if code_samples:
            # Safely get first 3 items
            items_list = list(code_samples.items())
            sample_files = dict(items_list[:3])
            code_samples_str = json.dumps(sample_files, indent=2)[:2000]
        else:
            code_samples_str = "{}"

        # Safely get repository tree
        repo_tree = repo_content.get('repository_tree', [])
        if repo_tree:
            tree_subset = repo_tree[:15]
            tree_str = json.dumps(tree_subset, indent=2)[:1500]
        else:
            tree_str = "[]"

        prompt = f"""
You are an expert MCP tool architect. Analyze this repository and suggest 5-15 practical MCP tools.

Repository: {repo_name}
Description: {repo_desc}
Language: {repo_lang}

Key Files:
{code_samples_str}

File Structure:
{tree_str}

Return ONLY valid JSON in this exact format:
{{
    "categories": {{
        "CORE_OPERATIONS": [
            {{
                "tool_name": "example_tool",
                "description": "what this tool does",
                "business_value": "why it's useful"
            }}
        ],
        "DATA_MANAGEMENT": [],
        "INTEGRATION": [],
        "UTILITIES": [],
        "AUTOMATION": []
    }},
    "business_logic": {{
        "business_purpose": "what this repository does",
        "primary_domain": "the main domain",
        "core_operations": ["main operations"]
    }}
}}

Analyze the repository and return ONLY valid JSON in the exact format above. Focus on practical, implementable tools based on the actual code."""

        try:
            content = await self._call_ai_service(prompt, max_tokens=8000)
            content = content.strip()

            logger.info(f"Raw AI response length: {len(content)}")
            logger.debug(f"Raw AI response preview: {content[:500]}")

            # Extract JSON from response with improved parsing
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            elif '{' in content:
                # Find the first { and last } to extract JSON
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end > start:
                    content = content[start:end]

            # Clean up common JSON formatting issues
            content = content.replace('\n', ' ').replace('\r', ' ')
            content = ' '.join(content.split())  # Normalize whitespace

            logger.info(f"Cleaned content length: {len(content)}")

            parsed_result = json.loads(content)

            # Transform the result to match expected structure
            categories = parsed_result.get("categories", {})
            business_logic = parsed_result.get("business_logic", {})

            # Calculate total tools
            total_tools = sum(len(tools) for tools in categories.values() if isinstance(tools, list))

            return {
                "repository_analysis": {
                    "complexity_score": 75,
                    "primary_domain": business_logic.get("primary_domain", "unknown"),
                    "key_capabilities": business_logic.get("core_operations", []),
                    "technical_stack": [repo_lang],
                    "integration_points": [],
                    "business_processes": business_logic.get("core_operations", [])
                },
                "business_logic": business_logic,
                "workflows": {
                    "data_flows": [],
                    "business_processes": [],
                    "integration_patterns": [],
                    "automation_opportunities": []
                },
                "api_capabilities": {
                    "existing_apis": [],
                    "api_patterns": [],
                    "authentication": {"methods": [], "description": ""},
                    "external_integrations": [],
                    "potential_new_apis": []
                },
                "integration_opportunities": {
                    "current_integrations": [],
                    "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                    "external_services": [],
                    "integration_opportunities": [],
                    "data_transformations": []
                },
                "mcp_suggestions": {
                    "categories": categories,
                    "total_tools_suggested": total_tools,
                    "prioritized_recommendations": [],
                    "implementation_roadmap": {}
                }
            }

        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {str(e)}")
            # Return enhanced empty structure if analysis fails
            return {
                "repository_analysis": {
                    "complexity_score": 0,
                    "primary_domain": "unknown",
                    "key_capabilities": [],
                    "technical_stack": [],
                    "integration_points": [],
                    "business_processes": []
                },
                "business_logic": {
                    "primary_domain": "unknown",
                    "business_purpose": "Analysis failed",
                    "core_operations": []
                },
                "workflows": {
                    "data_flows": [],
                    "business_processes": [],
                    "integration_patterns": [],
                    "automation_opportunities": []
                },
                "api_capabilities": {
                    "existing_apis": [],
                    "api_patterns": [],
                    "authentication": {"methods": [], "description": ""},
                    "external_integrations": [],
                    "potential_new_apis": []
                },
                "integration_opportunities": {
                    "current_integrations": [],
                    "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                    "external_services": [],
                    "integration_opportunities": [],
                    "data_transformations": []
                },
                "mcp_suggestions": {
                    "categories": {},
                    "total_tools_suggested": 0,
                    "prioritized_recommendations": [],
                    "implementation_roadmap": {}
                }
            }

        prompt = f"""
You are an expert MCP tool architect. Analyze this repository and suggest 5-15 practical MCP tools.

Repository: {repo_name}
Description: {repo_desc}
Language: {repo_lang}

Key Files:
{code_samples_str}

File Structure:
{tree_str}

## Instructions

1. Look at the actual code files and understand what this repository does
2. Suggest 5-15 practical MCP tools based on the real functionality
3. Focus on tools that would be genuinely useful for Claude users
4. Each tool should be implementable from the existing code

## Tool Categories:
- CORE_OPERATIONS: Main business functions
- DATA_MANAGEMENT: Data processing and validation
- INTEGRATION: API calls and external services
- UTILITIES: Helper functions and formatters
- AUTOMATION: Workflow and deployment helpers

## Required JSON Output Format

{{
    "repository_analysis": {{
        "primary_domain": "what this repository does",
        "complexity_score": 75
    }},
    "total_tools_suggested": 8,
    "categories": {{
        "CORE_OPERATIONS": [
            {{
                "tool_name": "example_tool",
                "description": "what this tool does",
                "business_value": "why it's useful"
            }}
        ],
        "DATA_MANAGEMENT": [],
        "INTEGRATION": [],
        "UTILITIES": [],
        "AUTOMATION": []
    }},
    "business_logic": {{
        "business_purpose": "what this repository does",
        "primary_domain": "the main domain",
        "core_operations": ["main operations"]
    }}
}}

Analyze the repository and return ONLY valid JSON in the exact format above. Focus on practical, implementable tools based on the actual code."""
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=8000)
            content = content.strip()

            logger.info(f"Raw AI response length: {len(content)}")
            logger.debug(f"Raw AI response preview: {content[:500]}")

            # Extract JSON from response with improved parsing
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            elif '{' in content:
                # Find the first { and last } to extract JSON
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end > start:
                    content = content[start:end]

            # Clean up common JSON formatting issues
            content = content.replace('\n', ' ').replace('\r', ' ')
            content = ' '.join(content.split())  # Normalize whitespace

            # Additional JSON cleaning
            content = self._clean_json_response(content)

            if not content or content == '':
                raise ValueError("Empty response from AI service")

            result = json.loads(content)
            logger.info("Comprehensive analysis completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {str(e)}")
            # Return enhanced empty structure if analysis fails
            return {
                "repository_analysis": {
                    "complexity_score": 0,
                    "primary_domain": "unknown",
                    "key_capabilities": [],
                    "technical_stack": [],
                    "integration_points": [],
                    "business_processes": []
                },
                "tool_generation_strategy": {
                    "total_tools_recommended": 0,
                    "tool_distribution": {
                        "core_operations": 0,
                        "data_management": 0,
                        "integration": 0,
                        "utilities": 0,
                        "monitoring": 0,
                        "automation": 0,
                        "reporting": 0
                    }
                },
                "categories": {
                    "CORE_OPERATIONS": [],
                    "DATA_MANAGEMENT": [],
                    "INTEGRATION": [],
                    "UTILITIES": [],
                    "MONITORING": [],
                    "AUTOMATION": [],
                    "REPORTING": []
                },
                "prioritized_recommendations": [],
                "implementation_roadmap": {
                    "phase_1_tools": [],
                    "phase_2_tools": [],
                    "phase_3_tools": []
                },
                "business_logic": {
                    "primary_domain": "unknown",
                    "business_purpose": "Analysis failed",
                    "core_operations": [],
                    "business_entities": [],
                    "business_rules": [],
                    "target_users": [],
                    "use_cases": [],
                    "value_proposition": "Could not determine"
                },
                "workflows": {
                    "data_flows": [],
                    "business_processes": [],
                    "integration_patterns": [],
                    "automation_opportunities": []
                },
                "api_capabilities": {
                    "existing_apis": [],
                    "api_patterns": [],
                    "authentication": {"methods": [], "description": ""},
                    "external_integrations": [],
                    "potential_new_apis": []
                },
                "integration_opportunities": {
                    "current_integrations": [],
                    "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                    "external_services": [],
                    "integration_opportunities": [],
                    "data_transformations": []
                },
                "total_tools_suggested": 0,
                "estimated_total_effort_hours": 0
            }
        
    async def analyze_repository_comprehensively(
        self, 
        repo_owner: str,
        repo_name: str,
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive business logic analysis using OpenAI
        """
        try:
            logger.info(f"Starting comprehensive analysis for {repo_owner}/{repo_name}")
            
            # 1. Gather comprehensive repository content
            repo_content = await self._gather_repository_content(
                repo_owner, repo_name, github_token, repo_info
            )
            
            # Add small delay to make progress visible
            await asyncio.sleep(1)
            
            # 2-6. Perform analysis (single call for Claude, multiple for OpenAI)
            if self.use_claude:
                # Use comprehensive single-call analysis for Claude
                comprehensive_results = await self._analyze_everything_comprehensive(repo_content)
                # Add delay to show AI processing time
                await asyncio.sleep(2)
                business_analysis = comprehensive_results.get("business_logic", {})
                workflow_analysis = comprehensive_results.get("workflows", {}) 
                api_analysis = comprehensive_results.get("api_capabilities", {})
                integration_analysis = comprehensive_results.get("integration_opportunities", {})
                mcp_suggestions = comprehensive_results.get("mcp_suggestions", {})
            else:
                # For OpenAI, use separate calls with delays (rate limiting)
                business_analysis = await self._analyze_business_logic(repo_content)
                await asyncio.sleep(22)  # Rate limit: 3 requests/minute = 20s between calls + buffer
                
                workflow_analysis = await self._analyze_workflows(repo_content)
                await asyncio.sleep(22)
                
                api_analysis = await self._analyze_api_capabilities(repo_content)
                await asyncio.sleep(22)
                
                integration_analysis = await self._analyze_integration_points(repo_content)
                await asyncio.sleep(22)
                
                mcp_suggestions = await self._generate_mcp_suggestions({
                    "repository_info": repo_content["repository_info"],
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "apis": api_analysis,
                    "integrations": integration_analysis,
                    "code_samples": repo_content["code_samples"]
                })
            
            # 7. Calculate confidence and complexity scores
            confidence_score = self._calculate_analysis_confidence(mcp_suggestions)
            complexity_assessment = self._assess_implementation_complexity(mcp_suggestions)
            
            return {
                "comprehensive_analysis": {
                    "business_logic": business_analysis,
                    "workflows": workflow_analysis,
                    "api_capabilities": api_analysis,
                    "integration_opportunities": integration_analysis
                },
                "mcp_suggestions": mcp_suggestions,
                "confidence_score": confidence_score,
                "implementation_complexity": complexity_assessment,
                "analysis_metadata": {
                    "files_analyzed": repo_content.get("files_count", 0),
                    "analysis_timestamp": repo_content.get("analysis_timestamp"),
                    "repository_size": repo_content.get("repository_size", 0)
                },
                "repository_tree": repo_content.get("repository_tree", []),
                "repository_info": repo_content.get("repository_info", {})
            }
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed for {repo_owner}/{repo_name}: {str(e)}")
            raise Exception(f"AI-powered analysis failed: {str(e)}")
    
    async def _gather_repository_content(
        self, 
        repo_owner: str, 
        repo_name: str, 
        github_token: str,
        repo_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Gather comprehensive repository content for analysis"""
        
        # Get basic repository info if not provided
        if not repo_info:
            repo_info = self.github_service.get_repository_info_sync(
                repo_owner, repo_name, github_token
            )
        
        # Get repository tree and important files
        repo_tree = self.github_service.get_repository_tree_sync(
            repo_owner, repo_name, github_token, max_depth=3
        )
        
        # Identify key files to analyze
        important_files = self._identify_important_files(repo_tree)

        # Debug logging
        logger.info(f"Important files type: {type(important_files)}")
        logger.info(f"Important files count: {len(important_files) if hasattr(important_files, '__len__') else 'No len'}")

        # Ensure important_files is a list
        if not isinstance(important_files, list):
            logger.warning(f"important_files is not a list, it's {type(important_files)}, converting to list")
            important_files = list(important_files) if hasattr(important_files, '__iter__') else []

        # Get file contents for analysis
        code_samples = {}
        files_analyzed = 0

        # Ensure important_files is a list before slicing
        if not isinstance(important_files, list):
            important_files = []

        for file_path in important_files[:self.max_files_to_analyze]:
            try:
                file_content = self.github_service.get_file_content_sync(
                    repo_owner, repo_name, file_path, github_token
                )

                if file_content and len(file_content) <= self.max_file_size:
                    code_samples[file_path] = file_content
                    files_analyzed += 1

            except Exception as e:
                logger.warning(f"Failed to get content for {file_path}: {str(e)}")
                continue
        
        # Get nested tree structure for frontend display
        nested_tree = self.github_service.get_repository_tree_nested(
            repo_owner, repo_name, github_token, max_depth=3
        )
        
        return {
            "repository_info": repo_info,
            "repository_tree": nested_tree,
            "code_samples": code_samples,
            "files_count": files_analyzed,
            "repository_size": repo_info.get("size", 0),
            "analysis_timestamp": repo_info.get("updated_at")
        }
    
    def _identify_important_files(self, repo_tree: List[Dict]) -> List[str]:
        """Identify important files for business logic analysis"""
        
        important_patterns = [
            # Configuration and setup files
            "README.md", "setup.py", "pyproject.toml", "package.json", 
            "Cargo.toml", "go.mod", "pom.xml", "build.gradle",
            
            # Main application files
            "main.py", "app.py", "index.js", "server.js", "main.go",
            "__init__.py", "cli.py", "command.py",
            
            # API and service files
            "api/", "routes/", "handlers/", "controllers/", "services/",
            "endpoints/", "views/", "models/",
            
            # Documentation
            "docs/", "documentation/", "examples/", "CHANGELOG",
            
            # Configuration
            "config/", "settings/", "env", ".env.example"
        ]
        
        important_files = []
        
        for item in repo_tree:
            file_path = item.get("path", "")
            file_name = file_path.split("/")[-1]
            
            # Check if file matches important patterns
            for pattern in important_patterns:
                if (pattern in file_path.lower() or 
                    pattern in file_name.lower() or
                    file_path.startswith(pattern) or
                    file_name == pattern):
                    important_files.append(file_path)
                    break
        
        # Sort by importance (main files first, then config, then others)
        def file_importance(file_path):
            main_files = ["main.py", "app.py", "index.js", "server.js", "README.md"]
            if any(main in file_path.lower() for main in main_files):
                return 0
            elif "api" in file_path.lower() or "route" in file_path.lower():
                return 1
            elif "config" in file_path.lower() or "setup" in file_path.lower():
                return 2
            else:
                return 3
        
        important_files.sort(key=file_importance)
        return important_files
    
    async def _analyze_business_logic(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze repository's core business logic"""
        
        prompt = f"""
        Analyze this repository's core business logic and purpose:

        Repository Info:
        - Name: {repo_content['repository_info'].get('name')}
        - Description: {repo_content['repository_info'].get('description')}
        - Language: {repo_content['repository_info'].get('language')}
        - Topics: {repo_content['repository_info'].get('topics', [])}

        Key Files Content:
        {json.dumps(dict(list(repo_content.get('code_samples', {}).items())[:5]) if repo_content.get('code_samples') else {}, indent=2)}

        Please analyze and identify:
        1. Primary business domain (e.g., document processing, data analysis, web scraping, etc.)
        2. Core business operations and their purposes
        3. Key business entities and data models
        4. Business rules and logic patterns
        5. Value proposition - what business problem does this solve?
        6. Target users and use cases
        7. Business processes and workflows

        Return as JSON with this structure:
        {{
            "primary_domain": "string",
            "business_purpose": "detailed description",
            "core_operations": [
                {{
                    "name": "operation_name",
                    "purpose": "what it does for business",
                    "business_value": "why it matters",
                    "complexity": "low|medium|high"
                }}
            ],
            "business_entities": ["entity1", "entity2"],
            "business_rules": ["rule1", "rule2"],
            "target_users": ["user_type1", "user_type2"],
            "use_cases": [
                {{
                    "scenario": "business scenario",
                    "user_story": "as a user, I want to...",
                    "business_outcome": "expected result"
                }}
            ],
            "value_proposition": "core business value"
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Business logic analysis failed: {str(e)}")
            return {
                "primary_domain": "unknown",
                "business_purpose": "Analysis failed",
                "core_operations": [],
                "business_entities": [],
                "business_rules": [],
                "target_users": [],
                "use_cases": [],
                "value_proposition": "Could not determine"
            }
    
    async def _analyze_workflows(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze workflow patterns and business processes"""
        
        prompt = f"""
        Analyze the workflow patterns and business processes in this repository:

        Code Samples:
        {json.dumps(dict(list(repo_content.get('code_samples', {}).items())[:8]) if repo_content.get('code_samples') else {}, indent=2)}

        Identify:
        1. Data flow patterns (input → processing → output)
        2. Business process workflows
        3. Integration points with external systems
        4. Automation opportunities
        5. Decision points and business rules
        6. Error handling and recovery processes

        Return as JSON:
        {{
            "data_flows": [
                {{
                    "name": "flow_name",
                    "description": "what the flow does",
                    "inputs": ["input1", "input2"],
                    "processing_steps": ["step1", "step2"],
                    "outputs": ["output1", "output2"],
                    "business_impact": "why this flow matters"
                }}
            ],
            "business_processes": [
                {{
                    "process_name": "name",
                    "description": "what it accomplishes",
                    "steps": ["step1", "step2"],
                    "automation_level": "manual|semi-automated|automated",
                    "improvement_opportunities": ["opportunity1"]
                }}
            ],
            "integration_patterns": [
                {{
                    "integration_type": "API|Database|File|etc",
                    "description": "what it integrates with",
                    "data_exchanged": ["data_type1"],
                    "frequency": "real-time|batch|on-demand"
                }}
            ],
            "automation_opportunities": [
                {{
                    "opportunity": "what could be automated",
                    "current_state": "how it works now",
                    "potential_benefit": "business value of automation",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Workflow analysis failed: {str(e)}")
            return {
                "data_flows": [],
                "business_processes": [],
                "integration_patterns": [],
                "automation_opportunities": []
            }
    
    async def _analyze_api_capabilities(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze API patterns and capabilities"""
        
        prompt = f"""
        Analyze the API capabilities and patterns in this repository:

        Code Samples:
        {json.dumps(dict(list(repo_content.get('code_samples', {}).items())[:10]) if repo_content.get('code_samples') else {}, indent=2)}

        Identify:
        1. Existing API endpoints and their purposes
        2. API design patterns used
        3. Authentication and authorization methods
        4. Data formats and schemas
        5. Rate limiting and performance considerations
        6. External API integrations
        7. Potential new API endpoints that could be created

        Return as JSON:
        {{
            "existing_apis": [
                {{
                    "endpoint": "/api/endpoint",
                    "method": "GET|POST|PUT|DELETE",
                    "purpose": "what it does",
                    "input_schema": {{"param": "type"}},
                    "output_schema": {{"result": "type"}},
                    "business_function": "business purpose"
                }}
            ],
            "api_patterns": [
                {{
                    "pattern_name": "REST|GraphQL|RPC|etc",
                    "usage": "how it's used",
                    "benefits": "advantages for business"
                }}
            ],
            "authentication": {{
                "methods": ["API_KEY", "OAuth", "JWT"],
                "description": "how auth works"
            }},
            "external_integrations": [
                {{
                    "service": "external service name",
                    "purpose": "why it's integrated",
                    "api_type": "REST|GraphQL|etc",
                    "data_exchanged": ["data_type1"]
                }}
            ],
            "potential_new_apis": [
                {{
                    "proposed_endpoint": "/api/new-endpoint",
                    "purpose": "business need it would serve",
                    "value_proposition": "why create this API",
                    "complexity": "low|medium|high"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"API analysis failed: {str(e)}")
            return {
                "existing_apis": [],
                "api_patterns": [],
                "authentication": {"methods": [], "description": ""},
                "external_integrations": [],
                "potential_new_apis": []
            }
    
    async def _analyze_integration_points(self, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze integration opportunities and system connections"""
        
        prompt = f"""
        Analyze integration opportunities and system connection points:

        Repository: {repo_content['repository_info'].get('name')}
        Code Analysis:
        {json.dumps(dict(list(repo_content.get('code_samples', {}).items())[:8]) if repo_content.get('code_samples') else {}, indent=2)}

        Identify:
        1. Current system integrations
        2. Database connections and data management
        3. File system operations
        4. Network communications
        5. Third-party service integrations
        6. Potential new integration opportunities
        7. Data transformation and ETL processes

        Return as JSON:
        {{
            "current_integrations": [
                {{
                    "integration_name": "name",
                    "type": "Database|API|File|Service",
                    "purpose": "business reason",
                    "data_flow": "bidirectional|input|output",
                    "criticality": "high|medium|low"
                }}
            ],
            "data_management": {{
                "databases": ["db_type1", "db_type2"],
                "data_formats": ["JSON", "CSV", "XML"],
                "storage_patterns": ["pattern1", "pattern2"]
            }},
            "external_services": [
                {{
                    "service": "service_name",
                    "purpose": "why it's used",
                    "integration_method": "API|SDK|Direct",
                    "business_dependency": "high|medium|low"
                }}
            ],
            "integration_opportunities": [
                {{
                    "opportunity": "potential integration",
                    "business_value": "what business value it would provide",
                    "technical_approach": "how to implement",
                    "effort_required": "low|medium|high",
                    "roi_potential": "high|medium|low"
                }}
            ],
            "data_transformations": [
                {{
                    "transformation": "what data is transformed",
                    "from_format": "source format",
                    "to_format": "target format",
                    "business_purpose": "why transformation is needed"
                }}
            ]
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=2000)
            content = content.strip()
            
            # Try to extract JSON from the response if it contains other text
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Integration analysis failed: {str(e)}")
            return {
                "current_integrations": [],
                "data_management": {"databases": [], "data_formats": [], "storage_patterns": []},
                "external_services": [],
                "integration_opportunities": [],
                "data_transformations": []
            }
    
    async def _generate_mcp_suggestions(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate categorized MCP tool suggestions based on comprehensive analysis"""
        
        prompt = f"""
        Based on this comprehensive repository analysis, suggest specific MCP tools that would provide genuine business value:

        Analysis Data:
        {json.dumps(analysis_data, indent=2)}

        Generate MCP tool suggestions in these categories:
        1. CORE_BUSINESS_TOOLS: Direct business logic operations
        2. WORKFLOW_AUTOMATION: Process automation and orchestration  
        3. DATA_INTEGRATION: Data transformation and system integration
        4. ANALYTICS_INTELLIGENCE: Business intelligence and reporting
        5. COMPLIANCE_SECURITY: Compliance, audit, and security tools
        6. OPTIMIZATION_PERFORMANCE: Performance and cost optimization
        7. USER_EXPERIENCE: End-user interaction and interface tools

        For each tool, provide:
        - tool_name: Clear, descriptive name (snake_case)
        - category: Which category it belongs to
        - business_value: Why this tool would be valuable for AI assistants
        - complexity_level: low/medium/high implementation complexity
        - input_schema: Expected input parameters with types
        - output_schema: Expected output structure with types
        - implementation_hints: Key technical considerations for building this
        - use_cases: Specific scenarios where AI assistants would use this
        - dependencies: What from the original repo this tool would depend on
        - estimated_effort_hours: Realistic development time estimate

        Focus on tools that:
        - Solve real business problems identified in the analysis
        - Would be frequently used by AI assistants
        - Leverage the unique capabilities of this repository
        - Provide clear, measurable business value

        Return as valid JSON:
        {{
            "categories": {{
                "CORE_BUSINESS_TOOLS": [tool_objects],
                "WORKFLOW_AUTOMATION": [tool_objects],
                "DATA_INTEGRATION": [tool_objects],
                "ANALYTICS_INTELLIGENCE": [tool_objects],
                "COMPLIANCE_SECURITY": [tool_objects],
                "OPTIMIZATION_PERFORMANCE": [tool_objects],
                "USER_EXPERIENCE": [tool_objects]
            }},
            "prioritized_recommendations": [
                {{
                    "tool_name": "highest_value_tool",
                    "priority_score": 95,
                    "reasoning": "why this is highest priority"
                }}
            ],
            "implementation_roadmap": {{
                "phase_1_quick_wins": [
                    {{
                        "tool_name": "easy_tool",
                        "effort_hours": 8,
                        "business_impact": "high|medium|low"
                    }}
                ],
                "phase_2_core_features": [
                    {{
                        "tool_name": "medium_tool", 
                        "effort_hours": 24,
                        "business_impact": "high|medium|low"
                    }}
                ],
                "phase_3_advanced": [
                    {{
                        "tool_name": "complex_tool",
                        "effort_hours": 40,
                        "business_impact": "high|medium|low"
                    }}
                ]
            }},
            "total_tools_suggested": 0,
            "estimated_total_effort_hours": 0
        }}
        """
        
        try:
            content = await self._call_ai_service(prompt, max_tokens=4000)
            content = content.strip()

            logger.info(f"MCP suggestions raw response length: {len(content)}")

            # Improved JSON extraction
            if content.startswith('```json'):
                content = content.split('```json')[1].split('```')[0].strip()
            elif content.startswith('```'):
                content = content.split('```')[1].split('```')[0].strip()
            elif '{' in content:
                # Find the first { and last } to extract JSON
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end > start:
                    content = content[start:end]

            # Clean up common JSON formatting issues
            content = content.replace('\n', ' ').replace('\r', ' ')
            content = ' '.join(content.split())  # Normalize whitespace

            # Additional JSON cleaning
            content = self._clean_json_response(content)

            if not content or content == '':
                raise ValueError("Empty response from AI service")

            suggestions = json.loads(content)
            
            # Calculate totals
            total_tools = sum(len(tools) for tools in suggestions["categories"].values())
            total_effort = self._calculate_total_effort(suggestions)
            
            suggestions["total_tools_suggested"] = total_tools
            suggestions["estimated_total_effort_hours"] = total_effort
            
            return suggestions
            
        except Exception as e:
            logger.error(f"MCP suggestions generation failed: {str(e)}")
            return {
                "categories": {
                    "CORE_BUSINESS_TOOLS": [],
                    "WORKFLOW_AUTOMATION": [],
                    "DATA_INTEGRATION": [],
                    "ANALYTICS_INTELLIGENCE": [],
                    "COMPLIANCE_SECURITY": [],
                    "OPTIMIZATION_PERFORMANCE": [],
                    "USER_EXPERIENCE": []
                },
                "prioritized_recommendations": [],
                "implementation_roadmap": {
                    "phase_1_quick_wins": [],
                    "phase_2_core_features": [],
                    "phase_3_advanced": []
                },
                "total_tools_suggested": 0,
                "estimated_total_effort_hours": 0
            }
    
    def _calculate_total_effort(self, suggestions: Dict[str, Any]) -> int:
        """Calculate total effort hours from all suggested tools"""
        total_hours = 0
        
        for phase_tools in suggestions.get("implementation_roadmap", {}).values():
            for tool in phase_tools:
                total_hours += tool.get("effort_hours", 0)
                
        return total_hours
    
    def _calculate_analysis_confidence(self, mcp_suggestions: Dict[str, Any]) -> float:
        """Calculate confidence score for the analysis"""
        
        # Base confidence on number of tools suggested and roadmap completeness
        total_tools = mcp_suggestions.get("total_tools_suggested", 0)
        has_roadmap = bool(mcp_suggestions.get("implementation_roadmap", {}).get("phase_1_quick_wins"))
        has_priorities = bool(mcp_suggestions.get("prioritized_recommendations"))
        
        confidence = 0.0
        
        if total_tools > 0:
            confidence += min(total_tools * 0.05, 0.4)  # Up to 40% for having tools
        
        if has_roadmap:
            confidence += 0.3  # 30% for having implementation roadmap
            
        if has_priorities:
            confidence += 0.3  # 30% for having prioritized recommendations
            
        return min(confidence, 1.0)
    
    def _assess_implementation_complexity(self, mcp_suggestions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall implementation complexity"""
        
        roadmap = mcp_suggestions.get("implementation_roadmap", {})
        total_effort = mcp_suggestions.get("estimated_total_effort_hours", 0)
        
        # Determine complexity level
        if total_effort <= 40:
            complexity_level = "low"
        elif total_effort <= 120:
            complexity_level = "medium"
        else:
            complexity_level = "high"
        
        return {
            "overall_complexity": complexity_level,
            "total_estimated_hours": total_effort,
            "quick_wins_available": len(roadmap.get("phase_1_quick_wins", [])),
            "advanced_features_count": len(roadmap.get("phase_3_advanced", [])),
            "recommendation": self._get_complexity_recommendation(complexity_level, total_effort)
        }
    
    def _get_complexity_recommendation(self, complexity_level: str, total_effort: int) -> str:
        """Get recommendation based on complexity assessment"""
        
        if complexity_level == "low":
            return f"Great candidate for MCP server development. Estimated {total_effort} hours for full implementation."
        elif complexity_level == "medium":
            return f"Good MCP server candidate. Consider phased implementation over {total_effort} hours."
        else:
            return f"Complex but valuable MCP server. Recommend starting with quick wins, full implementation ~{total_effort} hours."