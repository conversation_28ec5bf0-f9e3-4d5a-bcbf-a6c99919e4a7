"""Remove mcp_servers table and unused features

Revision ID: 106c2fcc391c
Revises: c1a330812fae
Create Date: 2025-07-23 17:08:34.068145

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '106c2fcc391c'
down_revision = 'c1a330812fae'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_mcp_servers_id', table_name='mcp_servers')
    op.drop_index('ix_mcp_servers_name', table_name='mcp_servers')
    op.drop_table('mcp_servers')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('mcp_servers',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('github_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('category', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('supported_languages', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('dependencies', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('popularity_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('last_updated', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('is_verified', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('extra_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='mcp_servers_pkey')
    )
    op.create_index('ix_mcp_servers_name', 'mcp_servers', ['name'], unique=False)
    op.create_index('ix_mcp_servers_id', 'mcp_servers', ['id'], unique=False)
    # ### end Alembic commands ###