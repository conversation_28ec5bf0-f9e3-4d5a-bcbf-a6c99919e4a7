"""Add indexing and integration fields to repo_analyses

Revision ID: 593075433069
Revises: 106c2fcc391c
Create Date: 2025-07-26 02:00:44.839606

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '593075433069'
down_revision = '106c2fcc391c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('mcp_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('icon', sa.String(), nullable=True),
    sa.Column('color', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.<PERSON>KeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_mcp_categories_id'), 'mcp_categories', ['id'], unique=False)
    op.create_table('mcp_servers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('url', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('github_url', sa.String(), nullable=True),
    sa.Column('npm_url', sa.String(), nullable=True),
    sa.Column('documentation_url', sa.String(), nullable=True),
    sa.Column('stars', sa.Integer(), nullable=True),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('confidence_score', sa.Float(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('url')
    )
    op.create_index(op.f('ix_mcp_servers_category'), 'mcp_servers', ['category'], unique=False)
    op.create_index(op.f('ix_mcp_servers_id'), 'mcp_servers', ['id'], unique=False)
    op.create_index(op.f('ix_mcp_servers_name'), 'mcp_servers', ['name'], unique=False)
    op.create_table('detected_integrations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('analysis_id', sa.Integer(), nullable=False),
    sa.Column('integration_type', sa.String(), nullable=False),
    sa.Column('service_name', sa.String(), nullable=False),
    sa.Column('detection_method', sa.String(), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=False),
    sa.Column('file_locations', sa.JSON(), nullable=True),
    sa.Column('package_names', sa.JSON(), nullable=True),
    sa.Column('code_patterns', sa.JSON(), nullable=True),
    sa.Column('env_variables', sa.JSON(), nullable=True),
    sa.Column('migration_complexity', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('mcp_alternatives', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['analysis_id'], ['repo_analyses.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_detected_integrations_id'), 'detected_integrations', ['id'], unique=False)
    op.add_column('repo_analyses', sa.Column('indexing_status', sa.String(), nullable=True))
    op.add_column('repo_analyses', sa.Column('indexing_progress', sa.Integer(), nullable=True))
    op.add_column('repo_analyses', sa.Column('vector_db_id', sa.String(), nullable=True))
    op.add_column('repo_analyses', sa.Column('integration_analysis', sa.JSON(), nullable=True))
    op.add_column('repo_analyses', sa.Column('last_indexed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('repo_analyses', sa.Column('code_hash', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('repo_analyses', 'code_hash')
    op.drop_column('repo_analyses', 'last_indexed_at')
    op.drop_column('repo_analyses', 'integration_analysis')
    op.drop_column('repo_analyses', 'vector_db_id')
    op.drop_column('repo_analyses', 'indexing_progress')
    op.drop_column('repo_analyses', 'indexing_status')
    op.drop_index(op.f('ix_detected_integrations_id'), table_name='detected_integrations')
    op.drop_table('detected_integrations')
    op.drop_index(op.f('ix_mcp_servers_name'), table_name='mcp_servers')
    op.drop_index(op.f('ix_mcp_servers_id'), table_name='mcp_servers')
    op.drop_index(op.f('ix_mcp_servers_category'), table_name='mcp_servers')
    op.drop_table('mcp_servers')
    op.drop_index(op.f('ix_mcp_categories_id'), table_name='mcp_categories')
    op.drop_table('mcp_categories')
    # ### end Alembic commands ###