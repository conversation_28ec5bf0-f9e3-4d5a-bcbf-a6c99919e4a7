# SuperMCP Production Deployment Guide

Complete guide for deploying SuperMCP to production environments.

## 🏗️ Architecture Overview

SuperMCP uses a microservices architecture with the following components:

- **Nginx**: Reverse proxy with SSL termination and load balancing
- **Next.js Frontend**: React-based frontend with server-side rendering
- **Fast<PERSON>I Backend**: Python API server with async support
- **PostgreSQL**: Primary database for persistent data
- **Redis**: Caching and message broker for Celery
- **Celery**: Background task processing for repository analysis
- **Flower**: Celery monitoring dashboard

## 🚀 Quick Deployment

### Prerequisites

- Docker and Docker Compose
- Domain name (for SSL)
- SSL certificates (Let's Encrypt recommended)
- GitHub OAuth App configured for production

### 1. <PERSON>lone and Configure

```bash
git clone https://github.com/your-org/supermcp.git
cd supermcp
cp .env.prod .env.prod.local
```

### 2. Update Environment Variables

Edit `.env.prod.local` with your production values:

```bash
# Database
POSTGRES_PASSWORD=your_secure_password_here

# Security Keys (generate with: openssl rand -hex 32)
JWT_SECRET=your_jwt_secret_32_chars_minimum
SECRET_KEY=your_secret_key_32_chars_minimum

# GitHub OAuth
GITHUB_CLIENT_ID=your_production_github_client_id
GITHUB_CLIENT_SECRET=your_production_github_client_secret

# External APIs
TAVILY_API_KEY=your_tavily_api_key
CONTEXT7_MCP_URL=your_context7_mcp_url

# Monitoring
FLOWER_USER=admin
FLOWER_PASSWORD=your_secure_flower_password
```

### 3. SSL Configuration

Place your SSL certificates in the `nginx/ssl/` directory:

```bash
mkdir -p nginx/ssl
# Copy your certificates
cp /path/to/your/cert.pem nginx/ssl/
cp /path/to/your/key.pem nginx/ssl/
```

### 4. Deploy

```bash
./deploy.sh
```

## 🔧 Manual Deployment

### Step 1: Build Images

```bash
docker-compose -f docker-compose.prod.yml build --no-cache
```

### Step 2: Start Database Services

```bash
docker-compose -f docker-compose.prod.yml up -d postgres redis
```

### Step 3: Run Migrations

```bash
docker-compose -f docker-compose.prod.yml run --rm backend alembic upgrade head
```

### Step 4: Start All Services

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔒 Security Configuration

### SSL/TLS Setup

1. **Let's Encrypt (Recommended)**:
   ```bash
   # Install certbot
   sudo apt install certbot
   
   # Get certificates
   sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com
   
   # Copy certificates
   sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/cert.pem
   sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/key.pem
   ```

2. **Custom Certificates**:
   - Place your certificate file as `nginx/ssl/cert.pem`
   - Place your private key as `nginx/ssl/key.pem`

### Security Headers

The Nginx configuration includes security headers:
- HSTS (Strict Transport Security)
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Content Security Policy
- Rate limiting for API endpoints

### Database Security

- PostgreSQL runs in isolated Docker network
- Strong password required (configured in environment)
- Connection limited to application containers only

## 📊 Monitoring and Logging

### Available Monitoring

1. **Flower (Celery Tasks)**: `http://your-domain:5555`
   - Monitor background task queue
   - View task history and performance
   - Basic auth protected

2. **Application Logs**:
   ```bash
   # View all logs
   docker-compose -f docker-compose.prod.yml logs -f
   
   # Service-specific logs
   docker-compose -f docker-compose.prod.yml logs -f backend
   docker-compose -f docker-compose.prod.yml logs -f frontend
   docker-compose -f docker-compose.prod.yml logs -f nginx
   ```

3. **Health Checks**:
   - Backend: `GET /api/v1/health`
   - Database: Built-in PostgreSQL health check
   - Redis: Built-in Redis health check

### Log Rotation

Configure log rotation for Nginx:

```bash
# Add to /etc/logrotate.d/nginx
/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
}
```

## 🔄 Backup and Recovery

### Database Backup

```bash
# Create backup
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U supermcp supermcp > backup.sql

# Restore backup
docker-compose -f docker-compose.prod.yml exec -T postgres psql -U supermcp supermcp < backup.sql
```

### Automated Backups

Add to crontab for daily backups:

```bash
# Daily database backup at 2 AM
0 2 * * * cd /path/to/supermcp && docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U supermcp supermcp | gzip > backups/supermcp-$(date +\%Y\%m\%d).sql.gz
```

## 🔧 Maintenance

### Updates

```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d

# Run any new migrations
docker-compose -f docker-compose.prod.yml run --rm backend alembic upgrade head
```

### Scaling

Scale Celery workers based on load:

```bash
# Scale to 3 worker instances
docker-compose -f docker-compose.prod.yml up -d --scale celery-worker=3
```

### Resource Monitoring

Monitor resource usage:

```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Clean up unused resources
docker system prune
```

## 🛠️ Troubleshooting

### Common Issues

1. **502 Bad Gateway**:
   - Check if backend service is running
   - Verify backend health endpoint
   - Check Docker network connectivity

2. **Database Connection Errors**:
   - Ensure PostgreSQL is healthy
   - Verify DATABASE_URL environment variable
   - Check if migrations have been run

3. **Celery Tasks Not Processing**:
   - Check Redis connectivity
   - Verify Celery worker is running
   - Check Flower dashboard for task status

4. **GitHub OAuth Errors**:
   - Verify OAuth app configuration
   - Check redirect URLs match production domain
   - Ensure client ID and secret are correct

### Debug Commands

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Execute commands in containers
docker-compose -f docker-compose.prod.yml exec backend bash
docker-compose -f docker-compose.prod.yml exec postgres psql -U supermcp supermcp

# View detailed logs
docker-compose -f docker-compose.prod.yml logs --tail=100 -f backend
```

## 🔐 Environment Variables Reference

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `POSTGRES_PASSWORD` | PostgreSQL password | `secure_password_123` |
| `JWT_SECRET` | JWT signing secret | `32_character_secret_key` |
| `SECRET_KEY` | App secret key | `32_character_secret_key` |
| `GITHUB_CLIENT_ID` | GitHub OAuth client ID | `********************` |
| `GITHUB_CLIENT_SECRET` | GitHub OAuth secret | `ae42c3dfe8a78a860e62d7af979f4687a23b6422` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `REDIS_PASSWORD` | Redis password | None (no auth) |
| `TAVILY_API_KEY` | Tavily search API key | None |
| `CONTEXT7_MCP_URL` | Context7 MCP server URL | None |
| `FLOWER_USER` | Flower basic auth user | `admin` |
| `FLOWER_PASSWORD` | Flower basic auth password | Required if accessing Flower |

## 📈 Performance Optimization

### Production Optimizations Included

1. **Frontend**:
   - Next.js standalone output for minimal container size
   - Static asset optimization and caching
   - Gzip compression via Nginx

2. **Backend**:
   - Gunicorn with multiple workers
   - Connection pooling for database
   - Redis caching for session data

3. **Nginx**:
   - HTTP/2 support
   - Gzip compression
   - Static file caching with long expiry
   - Rate limiting for API endpoints

4. **Database**:
   - PostgreSQL with optimized settings
   - Connection pooling
   - Regular VACUUM and ANALYZE

### Additional Optimizations

Consider these for high-traffic deployments:

1. **CDN Integration**: Use CloudFlare or AWS CloudFront
2. **Database Replica**: Set up read replicas for scaling
3. **Redis Cluster**: Scale Redis for high availability
4. **Load Balancer**: Add multiple backend instances
5. **Monitoring**: Integrate with Prometheus/Grafana

## 🆘 Support

For deployment issues:

1. Check the troubleshooting section above
2. Review application logs for specific errors
3. Consult the GitHub repository issues
4. Contact support with log details and configuration

Remember to never share sensitive information like passwords or API keys in support requests.