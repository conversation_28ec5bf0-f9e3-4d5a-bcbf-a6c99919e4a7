#!/bin/bash

# SuperMCP Production Deployment Script
set -e

echo "🚀 Starting SuperMCP Production Deployment..."

# Check if required environment file exists
if [ ! -f ".env.prod" ]; then
    echo "❌ Error: .env.prod file not found!"
    echo "Please copy .env.prod template and configure with your production values"
    exit 1
fi

# Check if Dock<PERSON> and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Error: Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Error: Docker Compose is not installed"
    exit 1
fi

# Load environment variables
export $(cat .env.prod | xargs)

echo "📦 Building production images..."
docker-compose -f docker-compose.prod.yml build --no-cache

echo "🗄️ Setting up database..."
docker-compose -f docker-compose.prod.yml up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🔄 Running database migrations..."
docker-compose -f docker-compose.prod.yml run --rm backend alembic upgrade head

echo "🌐 Starting all services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Health checks
echo "🏥 Performing health checks..."

# Check backend health
if curl -f http://localhost/api/v1/health > /dev/null 2>&1; then
    echo "✅ Backend health check passed"
else
    echo "❌ Backend health check failed"
    docker-compose -f docker-compose.prod.yml logs backend
    exit 1
fi

# Check frontend
if curl -f http://localhost > /dev/null 2>&1; then
    echo "✅ Frontend health check passed"
else
    echo "❌ Frontend health check failed"
    docker-compose -f docker-compose.prod.yml logs frontend
    exit 1
fi

# Check Celery worker
if docker-compose -f docker-compose.prod.yml exec -T celery-worker celery -A app.tasks.celery_app inspect ping > /dev/null 2>&1; then
    echo "✅ Celery worker health check passed"
else
    echo "❌ Celery worker health check failed"
    docker-compose -f docker-compose.prod.yml logs celery-worker
fi

echo ""
echo "🎉 SuperMCP has been successfully deployed!"
echo ""
echo "📋 Deployment Summary:"
echo "  🌐 Application: http://localhost (or your domain)"
echo "  🔧 API: http://localhost/api/v1"
echo "  📊 Flower (Celery monitoring): http://localhost:5555"
echo "  🗄️ Database: PostgreSQL (internal)"
echo "  📡 Redis: Available (internal)"
echo ""
echo "📚 Useful Commands:"
echo "  View logs: docker-compose -f docker-compose.prod.yml logs -f [service]"
echo "  Stop: docker-compose -f docker-compose.prod.yml down"
echo "  Restart: docker-compose -f docker-compose.prod.yml restart [service]"
echo "  Update: git pull && ./deploy.sh"
echo ""
echo "🔐 Security Notes:"
echo "  - Make sure to configure SSL certificates in nginx/ssl/"
echo "  - Update default passwords in .env.prod"
echo "  - Configure firewall rules for production"
echo "  - Set up monitoring and backup solutions"
echo ""
echo "✨ Happy analyzing! ✨"